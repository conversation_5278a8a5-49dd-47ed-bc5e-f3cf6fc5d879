package se.firme.ms.models.service.helper;
import java.util.List;
import java.util.Arrays;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.DatosEmailMultipleFirmaDTO;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.TokenServiceImpl;
import se.firme.ms.models.service.interfaz.IArchivoFirmaService;
import se.firme.ms.models.service.interfaz.ITokenService;
import se.firme.ms.models.service.interfaz.IUsuarioService;
import se.firme.ms.datos.models.entity.Token;

@Component
public class EmailDataHelper {
    
    private static Logger logger = Logger.getLogger(EmailDataHelper.class.getName());
    
    @Autowired
    private IUsuarioService usuarioService;

    @Autowired
    private  TokenServiceImpl tokenServiceImpl;

    @Autowired
    private IArchivoFirmaService archivoFirmaService;
    
    // USADO EN: TokenGenerationService, FirmaOrdenService
    public DatosEmailMultipleFirmaDTO extraerDatos(Long idUsuario, String emailFirmante, 
            String nombreFirmante, List<String> nombresDocumentos) throws FirmaException {
        // obtención de datos del remitente
        try {
            // devuelve null si no encuentra y si falla algo sube el error
            Usuario remitente = usuarioService.findById(idUsuario);
            if (remitente == null) {
                throw new FirmaException("Error critco: Remitente no encontrado " );
            }
            // validar que tengan los datos
            String nombreRemitente = remitente.getNombreCompleto();
            if(nombreRemitente == null || nombreRemitente.trim().isEmpty()) {
                 throw new FirmaException("Error critico: Remitente sin nombre");
            }
            String emailRemitente = remitente.getCorreoElectronico();
            if(emailRemitente == null || emailRemitente.trim().isEmpty()) {
                throw new FirmaException("Error critico: Remitente sin correo electrónico");
            }
            // 2. OBTENER NOMBRE DEL FIRMANTE
            String nombreFirmanteReal = nombreFirmante != null && !nombreFirmante.trim().isEmpty() ? 
                    nombreFirmante : obtenerNombreFirmante(emailFirmante);
            
            // 3. CREAR Y CONFIGURAR DTO
            DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
            datos.setNombreDocumento(nombresDocumentos);
            datos.setNombreFirmante(nombreFirmanteReal);
            datos.setNombreRemitente(nombreRemitente);
            datos.setEmailRemitente(emailRemitente);

            logger.info("Datos extraídos exitosamente - Remitente: " + nombreRemitente + 
                    ", Firmante: " + nombreFirmanteReal);
            
            return datos;
                    
        } catch (FirmaException e) {
            // Se lanza la excepción que se obtuvo arriba
            logger.warning("Error de validación: " + e.getMessage());
            throw e; 
        }
        catch (Exception e) {
            logger.severe("Error técnico inesperado obteniendo firmante: " + e.getMessage());
            throw new FirmaException("Error técnico obteniendo datos del firmante: " + e.getMessage());
        }
        
    }

    // Método para el reenvio buscando los datos a traves del token, FirmaNegocio.reenviarSolicitudFirma
    public DatosEmailMultipleFirmaDTO extraerDatosDesdeToken(Token token) throws FirmaException{
            logger.info("Extrayendo datos del dto desde el token");
        if (token == null) {
            throw new FirmaException("Token no puede ser null");
        }
        try {

            Usuario remitente = token.getIdUsuario();
            if (remitente == null) {
                throw new FirmaException("Token sin usuario remitente asociado - ID Token: " + token.getIdToken());
            }
            String nombreRemitente = remitente.getNombreCompleto();
            String emailRemitente = remitente.getCorreoElectronico();
            
            // 2. DATOS DEL FIRMANTE (desde token.getEmailFirmante())
            String nombreFirmante = obtenerNombreFirmante(token.getEmailFirmante());
            
            // 3. NOMBRES REALES DE DOCUMENTOS (desde token.getIds())
            List<String> nombresDocumentos = obtenerNombresRealesDocumentos(token.getIds());
            
            // 4. CREAR DTO COMPLETO
            DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
            datos.setNombreDocumento(nombresDocumentos);
            datos.setNombreFirmante(nombreFirmante);
            datos.setNombreRemitente(nombreRemitente);
            datos.setEmailRemitente(emailRemitente);
            
            logger.info(" Datos extraídos desde token:");
            logger.info("  Remitente: " + nombreRemitente + " (" + emailRemitente + ")");
            logger.info("  Firmante: " + nombreFirmante + " (" + token.getEmailFirmante() + ")");
            logger.info("  Documentos: " + nombresDocumentos);
            
            return datos;
                
        } catch (FirmaException e) {
            // Re-lanzar excepciones específicas de validación
            logger.severe(" Error de validación: " + e.getMessage());
            throw e;
        }
        catch (Exception e) {
            logger.severe("Error técnico inesperado: " + e.getMessage());
            throw new FirmaException("Error técnico procesando token: " + e.getMessage());
        }
    }
    
    private String obtenerNombreFirmante(String email) throws FirmaException{
        if (email == null || email.trim().isEmpty()) {
            throw new FirmaException("Email del firmante es requerido");
        }
        try {
            Usuario firmante = usuarioService.findByEmail(email);
            // 5. VALIDAR QUE TENGA NOMBRE COMPLETO
            String nombreCompleto = firmante.getNombreCompleto();
            if (nombreCompleto == null || nombreCompleto.trim().isEmpty()) {
                throw new FirmaException("Firmante encontrado pero sin nombre completo: " + email);
            }
            logger.info("Nombre firmante obtenido exitosamente: " + nombreCompleto + " para email: " + email);
            return nombreCompleto;
        } catch (Exception e) {
            logger.severe("Error de validación obteniendo nombre firmante: " + e.getMessage());
            throw new FirmaException("Error critico en la obtención del datos del nombre del firmante: "+e.getMessage());
        }
    }
    // Obtener nombres de los documentos a partir del string ids 0-
    private List<String> obtenerNombresRealesDocumentos(String ids) throws FirmaException{
         if (ids == null || ids.trim().isEmpty()) {
            throw new FirmaException("IDs de documentos no pueden ser null o vacíos");
        }
        try {
            
                logger.info("Obteniendo nombres de los archivos: " + ids);
                
                //  quitar "0-" del inicio y "-0" del final
                String idsLimpios = ids;
                if (idsLimpios.startsWith("0-")) {
                    idsLimpios = idsLimpios.substring(2);
                }
                if (idsLimpios.endsWith("-0")) {
                    idsLimpios = idsLimpios.substring(0, idsLimpios.length() - 2);
                }
                // Usar el servicio de archivoFirma para encontrar los nombres
                List<ArchivoFirma> archivos = archivoFirmaService.buscarArchivosFirma(idsLimpios);
                
                //Extraer nombres 
                List<String> nombresReales = archivos.stream()
                        .map(ArchivoFirma::getNombreArchivo)
                        .collect(Collectors.toList());
                if (nombresReales.isEmpty()) {
                    throw new FirmaException("Archivos encontrados pero sin nombres válidos para IDs: " + idsLimpios);
                }
                logger.info("Nombres de los archivos encontrados: " + nombresReales);
                return nombresReales;
        } catch (Exception e) {
            logger.warning("Error obteniendo nombres reales: " + e.getMessage());
            throw new FirmaException("Error critico al obtener nombres de los archivos "+e.getMessage());
        }
    }
    
    // Método utilizado para sacar datos en el flujo enviarNotificacionSolicitud de emailNotificationService
    // EmailNotificationService.enviarNotificacionSolicitud y enviarNotificacionSolicitudSinAdjuntos
    public DatosEmailMultipleFirmaDTO extraerDatosOptimizado(String email, String nombreArchivo, String tokenString) throws FirmaException {
        logger.info("Extrayendo datos para enviarNotificacionSolicitud");

        if (email == null || email.trim().isEmpty()) {
            throw new FirmaException("Email del firmante es requerido");
        }
        if (tokenString == null || tokenString.trim().isEmpty()) {
            throw new FirmaException("Token string es requerido");
        }

        List<String> nombresDocumentos = nombreArchivo != null ? Arrays.asList(nombreArchivo) : Arrays.asList("Documento");
        String nombreFirmante = obtenerNombreFirmante(email);

        try {
            // captura solo FirmaException | NullPointerException 
            Token tokenEntity = tokenServiceImpl.findTokenByID(tokenString);
            Usuario remitente = tokenEntity.getIdUsuario();
            String nombreRemitente = remitente.getNombreCompleto();
            String emailRemitente = remitente.getCorreoElectronico();

            DatosEmailMultipleFirmaDTO datos = new DatosEmailMultipleFirmaDTO();
            datos.setNombreDocumento(nombresDocumentos);
            datos.setNombreFirmante(nombreFirmante);
            datos.setNombreRemitente(nombreRemitente);
            datos.setEmailRemitente(emailRemitente);

            logger.info("Datos para enviarNotificacionSolicitud extraídos correctamente.");
            return datos;
        } catch (FirmaException e) {
            logger.info("Fallo critico en la obtención de los datos del token");
            throw e;
        } catch (Exception e) {
            logger.warning("Error técnico en extracción de datos desde string token");
            throw new FirmaException("Error técnico al obtener datos desde string token: "+e.getMessage());
        }
    }

}