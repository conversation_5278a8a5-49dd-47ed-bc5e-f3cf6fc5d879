package se.firme.ms.models.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import se.firme.ms.datos.models.dao.IPlantillaDocumentoRepository;
import se.firme.ms.datos.models.dto.*;
import se.firme.ms.datos.models.entity.*;
import se.firme.ms.models.service.helper.EmailDataHelper;
import se.firme.ms.models.service.interfaz.IArchivoFirmaService;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.DatosEmailMultipleFirmaDTO;
import se.firme.commons.firmese.util.Utilities;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;

@Component
public class FirmaOrdenService {
    
    private static Logger logger = Logger.getLogger(FirmaOrdenService.class.getName());
    
    @Autowired
    private IArchivoFirmaService archivoFirmaService;
    
    @Autowired
    private SolicitudFirmaService solicitudFirmaService;
    
    @Autowired
    private Environment env;
    
    @Autowired
    private IPlantillaDocumentoRepository plantillaRepository;

    @Autowired
    private UsuarioValidationService usuarioValidationService;

    @Autowired
    private TokenGenerationService tokenGenerationService;
    
    @Autowired
    private EmailNotificationService emailNotificationService;

    @Autowired
    private TokenServiceImpl tokenService;

    @Autowired
    private EmailDataHelper emailDataHelper;

    
    @Transactional(readOnly = false, isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {
        Exception.class})
    public Map<String, Object> procesarSolicitudFirmaOrden(FirmaOrdenRequestDTO request, String ipAddress) throws FirmaException {
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> documentosProcesados = new ArrayList<>();
        List<Long> idsArchivosCreados = new ArrayList<>();
        List<String> nombresArchivos = new ArrayList<>();
        List<String> rutasCompletas = new ArrayList<>();
        
        try {
            logger.info("Iniciando procesamiento de solicitud de firma en orden");
            logger.info("Tipo de orden: " + request.getTipoOrden());
            logger.info("Cantidad de firmantes: " + (request.getFirmantes() != null ? request.getFirmantes().size() : 0));
            logger.info("Cantidad de documentos: " + (request.getDocumentos() != null ? request.getDocumentos().size() : 0));
            
            // Validar request
            validarRequest(request);
            
            // Procesar cada documento (crear archivos y solicitudes)
            for (DocumentoOrdenDTO documento : request.getDocumentos()) {
                Map<String, Object> resultadoDoc = procesarDocumentoSinTokens(documento, request, ipAddress);
                documentosProcesados.add(resultadoDoc);
                
                idsArchivosCreados.add((Long) resultadoDoc.get("idArchivo"));
                nombresArchivos.add((String) resultadoDoc.get("nombreArchivo"));
                rutasCompletas.add((String) resultadoDoc.get("rutaCompleta"));
            }
            
            // GENERAR TOKENS AGRUPADOS Y ENVIAR CORREOS
            Map<String, String> tokensGenerados = generarTokensAgrupadosParaFirmantes(
                idsArchivosCreados,
                request.getDocumentos().get(0).getIdUsuario(),
                request.getFirmantes(),
                request.getFechaVigencia(),
                nombresArchivos,
                rutasCompletas
            );
            
            // Construir respuesta
            response.put("success", true);
            response.put("message", "Solicitud de firma creada exitosamente para " + documentosProcesados.size() + " documento(s)");
            response.put("documentos", documentosProcesados);
            response.put("tipoOrden", request.getTipoOrden());
            response.put("tipoFirma", request.getTipoFirma());
            response.put("totalDocumentos", documentosProcesados.size());
            response.put("totalFirmantes", request.getFirmantes().size());
            response.put("tokensGenerados", tokensGenerados.size());
            response.put("tokens", new ArrayList<>(tokensGenerados.values()));
            response.put("firmantesNotificados", tokensGenerados.keySet());
            response.put("idsArchivos", idsArchivosCreados);
            
            logger.info("Solicitud de firma procesada exitosamente");
            logger.info("Total documentos: " + documentosProcesados.size());
            logger.info("Total tokens generados: " + tokensGenerados.size());
            
            return response;
            
        } catch (FirmaException e) {
            logger.severe("Error de firma: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.severe("Error inesperado: " + e.getMessage());
            throw new FirmaException("Error procesando solicitud de firma: " + e.getMessage());
        }
    }
    
    private void validarRequest(FirmaOrdenRequestDTO request) throws FirmaException {
        if (request.getFirmantes() == null || request.getFirmantes().isEmpty()) {
            throw new FirmaException("La lista de firmantes no puede estar vacía");
        }
        
        if (request.getDocumentos() == null || request.getDocumentos().isEmpty()) {
            throw new FirmaException("La lista de documentos no puede estar vacía");
        }
        
        // APLICAR VALORES POR DEFECTO SI NO SE ESPECIFICARON
        if (request.getTipoOrden() == null || request.getTipoOrden().trim().isEmpty()) {
            request.setTipoOrden("PARALELO");
        }
        
        // Aplicar valores por defecto a firmantes
        for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
            if (firmante.getRol() == null || firmante.getRol().trim().isEmpty()) {
                firmante.setRol("Firmante");
            }
            if (firmante.getOrden() <= 0) {
                firmante.setOrden(1);
            }
        }
        
        if (!"PARALELO".equals(request.getTipoOrden()) && !"SECUENCIAL".equals(request.getTipoOrden())) {
            throw new FirmaException("Tipo de orden debe ser PARALELO o SECUENCIAL");
        }
        
        // AJUSTAR ÓRDENES AUTOMÁTICAMENTE PARA TIPO PARALELO
        if ("PARALELO".equals(request.getTipoOrden())) {
            // Para tipo PARALELO, asignar orden 1 a todos
            for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
                firmante.setOrden(1);
            }
            logger.info("Tipo PARALELO: Todos los firmantes asignados con orden 1");
        } else {
            // Para tipo SECUENCIAL, validar órdenes únicos y consecutivos
            Set<Integer> ordenes = new HashSet<>();
            for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
                if (!ordenes.add(firmante.getOrden())) {
                    throw new FirmaException("Los órdenes de firma deben ser únicos para tipo SECUENCIAL");
                }
            }
            
            // Verificar que los órdenes sean consecutivos desde 1
            List<Integer> ordenesOrdenados = new ArrayList<>(ordenes);
            Collections.sort(ordenesOrdenados);
            for (int i = 0; i < ordenesOrdenados.size(); i++) {
                if (ordenesOrdenados.get(i) != i + 1) {
                    throw new FirmaException("Los órdenes de firma deben ser consecutivos comenzando desde 1 para tipo SECUENCIAL");
                }
            }
        }
        
        // Validar emails únicos
        Set<String> emails = new HashSet<>();
        for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
            if (firmante.getEmail() == null || firmante.getEmail().trim().isEmpty()) {
                throw new FirmaException("Todos los firmantes deben tener email");
            }
            if (!emails.add(firmante.getEmail().toLowerCase())) {
                throw new FirmaException("Los emails de los firmantes deben ser únicos");
            }
        }
        
        logger.info("Validación de request completada exitosamente");
        logger.info("Tipo de orden final: " + request.getTipoOrden());
    }
    
    private String generarRutaRelativa(long idUsuario) {
        Date fecha = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String fechaStr = sdf.format(fecha);
        return idUsuario + "/" + fechaStr + "/original/";
    }
    
    private String getRutaArchivos(boolean temporal) {
        String rutaBase = env.getProperty("routes.custom.file", "/opt/firmese/files");
        if (temporal) {
            return rutaBase + "/tmp/";
        } else {
            return rutaBase + "/";
        }
    }
    
    /**
     * Genera tokens agrupados para múltiples documentos/plantillas por firmante
     */
    private Map<String, String> generarTokensAgrupadosParaFirmantes(List<Long> idsArchivos, Long idUsuario, 
        List<FirmanteOrdenDTO> firmantes, String fechaVigencia, List<String> nombresArchivos, 
        List<String> rutasCompletas) throws FirmaException {
        
        // DELEGAR AL SERVICIO ESPECIALIZADO
        return tokenGenerationService.generarTokensAgrupadosParaFirmantes(
            idsArchivos, idUsuario, firmantes, fechaVigencia, nombresArchivos, rutasCompletas);
    }

/**
 * Determina si un archivo es una plantilla TyC
 */
private boolean esPlantillaTyC(Long idArchivo, String nombreArchivo) {
    try {
        // 1. Verificar por ID de plantilla conocido
        if (idArchivo != null) {
            // Consultar si es una plantilla TyC por ID
            try {
                ArchivoFirma archivo = archivoFirmaService.findById(idArchivo);
                if (archivo != null && archivo.getDescripcion() != null) {
                    String descripcion = archivo.getDescripcion().toLowerCase();
                    if (descripcion.contains("términos y condiciones") || 
                        descripcion.contains("terminos y condiciones") ||
                        descripcion.contains("tyc") ||
                        descripcion.contains("autorización datos personales") ||
                        descripcion.contains("autorizacion datos personales") ||
                        descripcion.contains("agregado automáticamente")) {
                        return true;
                    }
                }
            } catch (Exception e) {
                logger.warning("Error verificando archivo por ID: " + e.getMessage());
            }
        }
        
        // 2. Verificar por nombre de archivo
        if (nombreArchivo != null && !nombreArchivo.trim().isEmpty()) {
            String nombre = nombreArchivo.toLowerCase();
            if (nombre.contains("terminos") || 
                nombre.contains("términos") ||
                nombre.contains("tyc") ||
                nombre.contains("condiciones") ||
                nombre.contains("datos_personales") ||
                nombre.contains("autorizacion") ||
                nombre.contains("autorización")) {
                return true;
            }
        }
        
        return false;
        
    } catch (Exception e) {
        logger.warning("Error verificando si es plantilla TyC: " + e.getMessage());
        return false;
    }
}
    
    private List<SolicitudFirma> crearSolicitudesFirmaConOrden(Long idArchivoFirma, Long idUsuario, 
            List<FirmanteOrdenDTO> firmantes, String tipoOrden, String fechaVigencia) throws FirmaException {
        
        List<SolicitudFirma> solicitudes = new ArrayList<>();
        
        try {
            logger.info("Creando solicitudes de firma para archivo ID: " + idArchivoFirma);
            
            Date fechaVigenciaDate = null;
            if (fechaVigencia != null && !fechaVigencia.isEmpty()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                fechaVigenciaDate = sdf.parse(fechaVigencia);
                logger.info("Fecha de vigencia configurada: " + fechaVigenciaDate);
            }
            
            for (FirmanteOrdenDTO firmante : firmantes) {
                SolicitudFirma solicitudFirma = new SolicitudFirma();
                solicitudFirma.setIdArchivoFirma(idArchivoFirma);
                solicitudFirma.setIdUsuario(idUsuario);
                solicitudFirma.setEmailFirmante(firmante.getEmail());
                solicitudFirma.setFechaVencimiento(fechaVigenciaDate);
                solicitudFirma.setRolFirmante(firmante.getRol());
                solicitudFirma.setOrdenFirma(firmante.getOrden());
                solicitudFirma.setTipoOrdenFirma(tipoOrden);
                
                solicitudFirmaService.save(solicitudFirma);
                solicitudes.add(solicitudFirma);
                
                logger.info("Solicitud de firma creada para: " + firmante.getEmail() + 
                           " con orden: " + firmante.getOrden() + " y rol: " + firmante.getRol());
            }
            
            logger.info("Total de solicitudes de firma creadas: " + solicitudes.size());
            
        } catch (ParseException e) {
            logger.severe("Error parseando fecha de vigencia: " + e.getMessage());
            throw new FirmaException("Error en formato de fecha de vigencia: " + e.getMessage());
        } catch (Exception e) {
            logger.severe("Error creando solicitudes de firma: " + e.getMessage());
            throw new FirmaException("Error creando solicitudes de firma: " + e.getMessage());
        }
        
        return solicitudes;
    }
    
    private void guardarArchivo(byte[] archivoBytes, String rutaCompleta) throws FirmaException {
        FileOutputStream fos = null;
        try {
            logger.info("Guardando archivo en: " + rutaCompleta);
            fos = new FileOutputStream(rutaCompleta);
            fos.write(archivoBytes);
            logger.info("Archivo guardado exitosamente");
            
        } catch (IOException e) {
            logger.severe("Error guardando archivo: " + e.getMessage());
            throw new FirmaException("Error guardando archivo: " + e.getMessage());
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    logger.warning("Error cerrando FileOutputStream: " + e.getMessage());
                }
            }
        }
    }
    
    public List<SolicitudFirma> consultarSolicitudesPorArchivo(Long idArchivoFirma) throws FirmaException {
        try {
            return solicitudFirmaService.findAllByIdArchivoFirma(idArchivoFirma);
        } catch (Exception e) {
            logger.severe("Error consultando solicitudes por archivo: " + e.getMessage());
            throw new FirmaException("Error consultando solicitudes: " + e.getMessage());
        }
    }
    
    public boolean validarOrdenFirma(Long idArchivoFirma, String emailFirmante) throws FirmaException {
        try {
            // Obtener la solicitud del firmante actual
            SolicitudFirma solicitudActual = solicitudFirmaService.findByIdArchivoFirmaAndEmailFirmanteAndFirmadoIsFalse(idArchivoFirma, emailFirmante);
            
            if (solicitudActual == null) {
                throw new FirmaException("No se encontró solicitud de firma pendiente para el email: " + emailFirmante);
            }
            
            // Si es tipo PARALELO, siempre puede firmar
            if ("PARALELO".equals(solicitudActual.getTipoOrdenFirma())) {
                return true;
            }
            
            // Si es tipo SECUENCIAL, verificar que todos los órdenes anteriores estén completos
            if ("SECUENCIAL".equals(solicitudActual.getTipoOrdenFirma())) {
                List<SolicitudFirma> todasSolicitudes = solicitudFirmaService.findAllByIdArchivoFirma(idArchivoFirma);
                
                for (SolicitudFirma solicitud : todasSolicitudes) {
                    if (solicitud.getOrdenFirma() < solicitudActual.getOrdenFirma() && !solicitud.isFirmado()) {
                        logger.info("Orden anterior pendiente. Orden actual: " + solicitudActual.getOrdenFirma() + 
                                   ", Orden pendiente: " + solicitud.getOrdenFirma());
                        return false;
                    }
                }
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.severe("Error validando orden de firma: " + e.getMessage());
            throw new FirmaException("Error validando orden de firma: " + e.getMessage());
        }
    }

    /**
     * Procesa una solicitud de firma usando una plantilla existente
     */
    public Map<String, Object> procesarSolicitudConPlantilla(SolicitudFirmaPlantillaDTO request, String ipAddress) throws FirmaException {
    
        Map<String, Object> resultado = new HashMap<>();
        
        try {
            logger.info("=== PROCESANDO SOLICITUD CON PLANTILLA - TOKENS AGRUPADOS ===");
            logger.info("Plantilla ID: " + request.getIdPlantilla());
            logger.info("Firmantes: " + (request.getFirmantes() != null ? request.getFirmantes().size() : 0));
            
            // 1. Obtener la plantilla
            PlantillaDocumento plantilla = plantillaRepository.findByIdAndActiva(request.getIdPlantilla());
            if (plantilla == null) {
                throw new FirmaException("Plantilla no encontrada o inactiva");
            }
            
            logger.info("Plantilla encontrada: " + plantilla.getNombrePlantilla());
            
            // 2. Verificar que el archivo de la plantilla existe
            String rutaCompletaPlantilla = obtenerRutaCompletaPlantilla(plantilla);
            File archivoPlantilla = new File(rutaCompletaPlantilla);
            
            if (!archivoPlantilla.exists()) {
                throw new FirmaException("Archivo de plantilla no encontrado: " + rutaCompletaPlantilla);
            }
            
            logger.info("Archivo de plantilla existe: " + rutaCompletaPlantilla);
            
            // 3. Copiar archivo de plantilla a nueva ubicación
            String nuevaRutaRelativa = generarRutaRelativa(request.getIdUsuario());
            String nuevaRutaPrincipal = getRutaArchivos(true);
            String nuevoDirectorio = nuevaRutaPrincipal + nuevaRutaRelativa;
            String nuevaRutaCompleta = nuevoDirectorio + plantilla.getNombreArchivo();
            
            // Crear directorio y copiar archivo
            Utilities.crearDirectorio(nuevoDirectorio, true);
            Files.copy(archivoPlantilla.toPath(), Paths.get(nuevaRutaCompleta), StandardCopyOption.REPLACE_EXISTING);
            
            logger.info("Archivo copiado de plantilla: " + rutaCompletaPlantilla + " -> " + nuevaRutaCompleta);
            
            // 4. Crear registro en archivo_firma basado en la plantilla
            // Convertir SolicitudFirmaPlantillaDTO a SolicitudFirmaUnificadaDTO para compatibilidad
            SolicitudFirmaUnificadaDTO requestUnificada = new SolicitudFirmaUnificadaDTO();
            requestUnificada.setIdUsuario(request.getIdUsuario());
            requestUnificada.setFirmantes(request.getFirmantes());
            requestUnificada.setTipoOrden(request.getTipoOrden());
            requestUnificada.setFechaVigencia(request.getFechaVigencia());
            requestUnificada.setTipoFirma(request.getTipoFirma());
            requestUnificada.setDescripcionPersonalizada(request.getDescripcionPersonalizada());
            requestUnificada.setIdPlantilla(request.getIdPlantilla());

            ArchivoFirma archivoFirma = crearArchivoFirmaDesdePlantilla(plantilla, requestUnificada, nuevaRutaRelativa, ipAddress);
            
            logger.info("ArchivoFirma creado con ID: " + archivoFirma.getIdArchivoFirma());
            
            // 5. Crear solicitudes de firma SIN GENERAR TOKENS INDIVIDUALES
            List<SolicitudFirma> solicitudes = crearSolicitudesFirmaConOrden(
                archivoFirma.getIdArchivoFirma(),
                request.getIdUsuario(),
                request.getFirmantes(),
                request.getTipoOrden(),
                request.getFechaVigencia()
            );
            
            logger.info("Solicitudes de firma creadas: " + solicitudes.size());
            
            // 6. Generar UN SOLO token por firmante para la plantilla
            List<Long> idsArchivos = Arrays.asList(archivoFirma.getIdArchivoFirma());
            List<String> nombresArchivos = Arrays.asList(plantilla.getNombreArchivo());
            List<String> rutasCompletas = Arrays.asList(nuevaRutaCompleta);
            
            logger.info("=== GENERANDO TOKENS AGRUPADOS PARA PLANTILLA ===");
            logger.info("ID Archivo: " + archivoFirma.getIdArchivoFirma());
            logger.info("Nombre archivo: " + plantilla.getNombreArchivo());
            
            Map<String, String> tokensGenerados = generarTokensAgrupadosParaFirmantes(
                idsArchivos,
                request.getIdUsuario(),
                request.getFirmantes(),
                request.getFechaVigencia(),
                nombresArchivos,
                rutasCompletas
            );
            
            logger.info("=== TOKENS AGRUPADOS GENERADOS PARA PLANTILLA ===");
            logger.info("Total tokens: " + tokensGenerados.size());
            logger.info("Firmantes notificados: " + tokensGenerados.keySet());
            
            // 7. Construir respuesta
            resultado.put("success", true);
            resultado.put("message", "Solicitud de firma con plantilla creada exitosamente");
            resultado.put("idArchivo", archivoFirma.getIdArchivoFirma());
            resultado.put("idPlantilla", request.getIdPlantilla());
            resultado.put("nombreArchivo", plantilla.getNombreArchivo());
            resultado.put("nombrePlantilla", plantilla.getNombrePlantilla());
            resultado.put("hashArchivo", archivoFirma.getHashArchivo());
            resultado.put("solicitudesCreadas", solicitudes.size());
            resultado.put("tokensGenerados", tokensGenerados.size());
            resultado.put("tokens", new ArrayList<>(tokensGenerados.values()));
            resultado.put("tipoOrden", request.getTipoOrden());
            resultado.put("tipoFirma", archivoFirma.getTipoFirma());
            resultado.put("rutaRelativa", nuevaRutaRelativa);
            resultado.put("correosEnviados", tokensGenerados.size());
            resultado.put("notificacionesEnviadas", true);
            resultado.put("firmantesNotificados", tokensGenerados.keySet());
            resultado.put("tokenUnicoPorFirmante", true);
            resultado.put("procesamientoPlantilla", "TOKENS_AGRUPADOS");
            
            logger.info("=== PLANTILLA PROCESADA EXITOSAMENTE ===");
            logger.info("Token único generado para " + tokensGenerados.size() + " firmante(s)");
            
            return resultado;
            
        } catch (FirmaException e) {
            logger.severe("Error de firma procesando plantilla: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.severe("Error inesperado procesando plantilla: " + e.getMessage());
            e.printStackTrace();
            throw new FirmaException("Error procesando solicitud con plantilla: " + e.getMessage());
        }
    }
    
    /**
     * Crea un registro ArchivoFirma basado en una plantilla
     */
    private ArchivoFirma crearArchivoFirmaDesdePlantilla(PlantillaDocumento plantilla, SolicitudFirmaUnificadaDTO request, String nuevaRutaRelativa, String ipAddress) throws FirmaException {
        
        try {
            logger.info("Creando ArchivoFirma desde plantilla");
            
            // 1. GENERAR UID TEMPORAL
            String uidTemporal = UUID.randomUUID().toString();
            logger.info("UID temporal generado para hash único: " + uidTemporal);
            
            // 2. GENERAR HASH CON ARCHIVO + UID TEMPORAL
            String rutaPrincipal = getRutaArchivos(true);
            String rutaCompleta = rutaPrincipal + nuevaRutaRelativa + plantilla.getNombreArchivo();
            String hashUnico = generarHashUnicoConUID(rutaCompleta, uidTemporal);
            
            logger.info("Hash único generado: " + hashUnico);
            logger.info("UID temporal descartado (no se guarda en BD)");
            
            // Usar descripción personalizada si se proporciona
            String descripcion = (request.getDescripcionPersonalizada() != null && !request.getDescripcionPersonalizada().trim().isEmpty()) 
                ? request.getDescripcionPersonalizada() 
                : plantilla.getDescripcion();
            
            // 3. Crear registro ArchivoFirma CON HASH ÚNICO
            ArchivoFirma archivoFirma = archivoFirmaService.guardarArchivoFirma(
                plantilla.getNombreArchivo(),
                hashUnico, // Hash único generado con UID temporal
                request.getFirmantes().size(),
                1, // Estado pendiente
                ipAddress,
                request.getIdUsuario(),
                nuevaRutaRelativa,
                descripcion
            );
            
            // 4. Configurar campos adicionales del archivoFirma
            String tipoFirma = "MULTIPLE";
            if (plantilla.getTipoFirma() != null && !plantilla.getTipoFirma().trim().isEmpty()) {
                tipoFirma = plantilla.getTipoFirma();
            } else if (request.getTipoFirma() != null && !request.getTipoFirma().trim().isEmpty()) {
                tipoFirma = request.getTipoFirma();
            }
            archivoFirma.setTipoFirma(tipoFirma);
            
            // Configurar emails de firmantes
            StringBuilder emailFirmantes = new StringBuilder();
            for (int i = 0; i < request.getFirmantes().size(); i++) {
                if (i > 0) emailFirmantes.append(",");
                emailFirmantes.append(request.getFirmantes().get(i).getEmail());
            }
            archivoFirma.setEmailFirmantes(emailFirmantes.toString());
            
            // 5. Actualizar sin UID
            archivoFirmaService.update(archivoFirma);
            
            logger.info("ArchivoFirma creado exitosamente con ID: " + archivoFirma.getIdArchivoFirma() + 
                       " y hash único (UID temporal descartado)");
            
            return archivoFirma;
            
        } catch (Exception e) {
            logger.severe("Error creando ArchivoFirma desde plantilla: " + e.getMessage());
            throw new FirmaException("Error creando archivo de firma desde plantilla: " + e.getMessage());
        }
    }
    
    /**
     * Obtiene la ruta completa de una plantilla
     */
    private String obtenerRutaCompletaPlantilla(PlantillaDocumento plantilla) {
        String rutaBase = env.getProperty("routes.custom.file", "/opt/firmese/files");
        return rutaBase + "/plantillas/" + plantilla.getRutaRelativaArchivo();
    }

    /**
     * Consulta solicitudes de firma por usuario
     */
    public List<Map<String, Object>> consultarSolicitudesPorUsuario(Long idUsuario) throws FirmaException {
        try {
            logger.info("Consultando solicitudes de firma para usuario: " + idUsuario);
            
            // Usar método existente de ArchivoFirmaService para obtener archivos del usuario
            List<ArchivoFirma> archivosUsuario = archivoFirmaService.findByIdUsuario(idUsuario);
            
            List<Map<String, Object>> resultado = new ArrayList<>();
            
            for (ArchivoFirma archivo : archivosUsuario) {
                try {
                    Map<String, Object> info = new HashMap<>();
                    info.put("idArchivoFirma", archivo.getIdArchivoFirma());
                    info.put("nombreArchivo", archivo.getNombreArchivo() != null ? archivo.getNombreArchivo() : "");
                    info.put("descripcion", archivo.getDescripcion() != null ? archivo.getDescripcion() : "");
                    info.put("estado", archivo.getEstado());
                    info.put("cantidadFirmas", archivo.getCantidadFirmas());
                    info.put("cantidadFirmado", archivo.getCantidadFirmado());
                    info.put("emailFirmantes", archivo.getEmailFirmantes() != null ? archivo.getEmailFirmantes() : "");
                    info.put("hashArchivo", archivo.getHashArchivo() != null ? archivo.getHashArchivo() : "");
                    info.put("fechaRegistro", archivo.getFechaRegistro());
                    
                    // Obtener solicitudes para este archivo específico
                    List<SolicitudFirma> solicitudes = solicitudFirmaService.findAllByIdArchivoFirma(archivo.getIdArchivoFirma());
                    info.put("totalSolicitudes", solicitudes.size());
                    
                    int firmadas = 0;
                    for (SolicitudFirma solicitud : solicitudes) {
                        if (solicitud.isFirmado()) {
                            firmadas++;
                        }
                    }
                    info.put("solicitudesFirmadas", firmadas);
                    info.put("solicitudesPendientes", solicitudes.size() - firmadas);
                    
                    // Calcular progreso
                    if (!solicitudes.isEmpty()) {
                        double progreso = (double) firmadas / solicitudes.size() * 100;
                        info.put("progreso", Math.round(progreso * 100.0) / 100.0);
                    } else {
                        info.put("progreso", 0.0);
                    }
                    
                    resultado.add(info);
                    
                } catch (Exception e) {
                    logger.warning("Error procesando archivo " + archivo.getIdArchivoFirma() + ": " + e.getMessage());
                    // Continuar con los demás archivos
                }
            }
            
            logger.info("Consulta completada. Archivos encontrados: " + resultado.size());
            return resultado;
            
        } catch (Exception e) {
            logger.severe("Error consultando solicitudes por usuario: " + e.getMessage());
            throw new FirmaException("Error consultando solicitudes: " + e.getMessage());
        }
    }

    /**
     * Consulta estado detallado de una solicitud específica
     */
    public Map<String, Object> consultarEstadoSolicitud(Long idArchivoFirma) throws FirmaException {
        try {
            logger.info("Consultando estado de solicitud: " + idArchivoFirma);
            
            ArchivoFirma archivo = archivoFirmaService.findById(idArchivoFirma);
            if (archivo == null) {
                throw new FirmaException("Archivo de firma no encontrado: " + idArchivoFirma);
            }
            
            Map<String, Object> estado = new HashMap<>();
            estado.put("idArchivoFirma", archivo.getIdArchivoFirma());
            estado.put("nombreArchivo", archivo.getNombreArchivo());
            estado.put("descripcion", archivo.getDescripcion());
            estado.put("estado", archivo.getEstado());
            estado.put("cantidadFirmas", archivo.getCantidadFirmas());
            estado.put("cantidadFirmado", archivo.getCantidadFirmado());
            estado.put("hashArchivo", archivo.getHashArchivo());
            estado.put("fechaRegistro", archivo.getFechaRegistro());
            
            // Obtener detalles de solicitudes
            List<SolicitudFirma> solicitudes = solicitudFirmaService.findAllByIdArchivoFirma(idArchivoFirma);
            List<Map<String, Object>> detallesSolicitudes = new ArrayList<>();
            
            for (SolicitudFirma solicitud : solicitudes) {
                Map<String, Object> detalle = new HashMap<>();
                detalle.put("emailFirmante", solicitud.getEmailFirmante());
                detalle.put("rolFirmante", solicitud.getRolFirmante());
                detalle.put("ordenFirma", solicitud.getOrdenFirma());
                detalle.put("tipoOrdenFirma", solicitud.getTipoOrdenFirma());
                detalle.put("firmado", solicitud.isFirmado());
                detalle.put("fechaVencimiento", solicitud.getFechaVencimiento());
                detalle.put("fechaRegistro", solicitud.getFechaRegistro());
                
                detallesSolicitudes.add(detalle);
            }
            
            estado.put("solicitudes", detallesSolicitudes);
            estado.put("totalSolicitudes", solicitudes.size());
            
            // Contar solicitudes firmadas
            int firmadas = 0;
            int pendientes = 0;
            for (SolicitudFirma solicitud : solicitudes) {
                if (solicitud.isFirmado()) {
                    firmadas++;
                } else {
                    pendientes++;
                }
            }
            estado.put("solicitudesFirmadas", firmadas);
            estado.put("solicitudesPendientes", pendientes);
            
            // Calcular progreso
            if (!solicitudes.isEmpty()) {
                double progreso = (double) firmadas / solicitudes.size() * 100;
                estado.put("progreso", Math.round(progreso * 100.0) / 100.0);
            } else {
                estado.put("progreso", 0.0);
            }
            
            return estado;
            
        } catch (Exception e) {
            logger.severe("Error consultando estado de solicitud: " + e.getMessage());
            throw new FirmaException("Error consultando estado: " + e.getMessage());
        }
    }
    
    /**
     * Obtiene información detallada de una solicitud antes de eliminar
     */
    public Map<String, Object> obtenerInformacionSolicitud(Long idArchivoFirma) throws FirmaException {
        try {
            ArchivoFirma archivo = archivoFirmaService.findById(idArchivoFirma);
            if (archivo == null) {
                throw new FirmaException("Archivo de firma no encontrado: " + idArchivoFirma);
            }
            
            Map<String, Object> info = new HashMap<>();
            info.put("idArchivoFirma", archivo.getIdArchivoFirma());
            info.put("nombreArchivo", archivo.getNombreArchivo());
            info.put("descripcion", archivo.getDescripcion());
            info.put("estado", archivo.getEstado());
            info.put("cantidadFirmas", archivo.getCantidadFirmas());
            info.put("cantidadFirmado", archivo.getCantidadFirmado());
            info.put("hashArchivo", archivo.getHashArchivo());
            info.put("idUsuario", archivo.getIdUsuario());
            info.put("tipoFirma", archivo.getTipoFirma());
            
            // Obtener solicitudes relacionadas
            List<SolicitudFirma> solicitudes = solicitudFirmaService.findAllByIdArchivoFirma(idArchivoFirma);
            List<Map<String, Object>> detallesSolicitudes = new ArrayList<>();
            
            for (SolicitudFirma solicitud : solicitudes) {
                Map<String, Object> detalle = new HashMap<>();
                detalle.put("emailFirmante", solicitud.getEmailFirmante());
                detalle.put("rolFirmante", solicitud.getRolFirmante());
                detalle.put("ordenFirma", solicitud.getOrdenFirma());
                detalle.put("tipoOrdenFirma", solicitud.getTipoOrdenFirma());
                detalle.put("firmado", solicitud.isFirmado());
                detalle.put("fechaVencimiento", solicitud.getFechaVencimiento());
                
                detallesSolicitudes.add(detalle);
            }
            
            info.put("solicitudes", detallesSolicitudes);
            info.put("totalSolicitudes", solicitudes.size());
            
            // Determinar tipo de solicitud
            String tipoSolicitud = "ORIS";
            if (!solicitudes.isEmpty()) {
                String tipoOrden = solicitudes.get(0).getTipoOrdenFirma();
                if ("SECUENCIAL".equals(tipoOrden) || "PARALELO".equals(tipoOrden)) {
                    tipoSolicitud = "ORDEN_" + tipoOrden;
                }
            }
            
            if (archivo.getDescripcion() != null && archivo.getDescripcion().toLowerCase().contains("plantilla")) {
                tipoSolicitud = "PLANTILLA";
            }
            
            info.put("tipoSolicitud", tipoSolicitud);
            info.put("puedeEliminar", archivo.getCantidadFirmado() == 0);
            
            return info;
            
        } catch (Exception e) {
            logger.severe("Error obteniendo información de solicitud: " + e.getMessage());
            throw new FirmaException("Error obteniendo información: " + e.getMessage());
        }
    }
    
/**
 * Procesa una solicitud de firma unificada (plantilla o documentos nuevos)
 */
public Map<String, Object> procesarSolicitudUnificada(SolicitudFirmaUnificadaDTO request, String ipAddress) throws FirmaException {

    try {
        // Validaciones iniciales
        if (request == null) {
            throw new FirmaException("Request no puede ser null");
        }
        
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            ipAddress = "127.0.0.1"; // Valor por defecto
        }
        
        logger.info("=== PROCESANDO SOLICITUD UNIFICADA - TOKENS ÚNICOS ===");
        logger.info("Tiene plantilla: " + request.esPlantilla());
        logger.info("Tiene documentos nuevos: " + request.esDocumentoNuevo());
        logger.info("Firmantes: " + (request.getFirmantes() != null ? request.getFirmantes().size() : 0));
        
        // **DETECTAR TIPO DE USUARIO SOLICITANTE**
        String tipoUsuario = request.getTipoUsuarioDetectado();
        logger.info("🔍 Tipo detectado: " + tipoUsuario);
        
        // Validar request
        validarSolicitudUnificada(request);

        // **DETECTAR FIRMANTES NO REGISTRADOS ANTES DE PROCESAR**
        boolean tieneFirmantesNoRegistrados = verificarSiHayFirmantesNoRegistrados(request.getFirmantes());
        logger.info("🔍 Resultado detección firmantes no registrados: " + tieneFirmantesNoRegistrados);

        // **AGREGAR PLANTILLAS TyC AUTOMÁTICAMENTE PARA TODOS LOS FIRMANTES CON tyc=false**
        if (request.getFirmantes() != null && !request.getFirmantes().isEmpty()) {
            List<PlantillaRequestDTO> plantillasTyC = new ArrayList<>();
            // IDs de plantillas TyC (ajusta según tu base de datos)
            Long idPlantillaTyC = 1L;
            Long idPlantillaDatos = 2L;
            PlantillaDocumento plantillaTyC = null;
            PlantillaDocumento plantillaDatos = null;
            try {
                Optional<PlantillaDocumento> plantillaTyCOpt = plantillaRepository.findById(idPlantillaTyC);
                Optional<PlantillaDocumento> plantillaDatosOpt = plantillaRepository.findById(idPlantillaDatos);
                if (plantillaTyCOpt.isPresent()) plantillaTyC = plantillaTyCOpt.get();
                if (plantillaDatosOpt.isPresent()) plantillaDatos = plantillaDatosOpt.get();
            } catch (Exception e) {
                logger.warning("Error obteniendo plantillas TyC: " + e.getMessage());
            }
            for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
                boolean necesitaTyC = true;
                try {
                    Usuario usuario = usuarioValidationService.obtenerUsuarioPorEmail(firmante.getEmail());
                    if (usuario != null && usuario.getFirmadoTyc()) {
                        necesitaTyC = false;
                    }
                } catch (Exception ex) {
                    logger.warning("Error consultando firmadoTyc para usuario: " + firmante.getEmail() + " - " + ex.getMessage());
                }
                if (necesitaTyC) {
                    if (plantillaTyC != null) {
                        PlantillaRequestDTO plantillaTyCRequest = new PlantillaRequestDTO();
                        plantillaTyCRequest.setIdPlantilla(idPlantillaTyC);
                        plantillaTyCRequest.setDescripcion("Términos y Condiciones (agregado automáticamente)");
                        plantillasTyC.add(plantillaTyCRequest);
                    }
                    if (plantillaDatos != null) {
                        PlantillaRequestDTO plantillaDatosRequest = new PlantillaRequestDTO();
                        plantillaDatosRequest.setIdPlantilla(idPlantillaDatos);
                        plantillaDatosRequest.setDescripcion("Autorización Datos Personales (agregado automáticamente)");
                        plantillasTyC.add(plantillaDatosRequest);
                    }
                    logger.info("✅ Plantillas TyC/Datos agregadas automáticamente para: " + firmante.getEmail());
                }
            }
            // Agregar las plantillas TyC/Datos al inicio de la lista si no estaban ya
            if (request.getPlantillas() == null) request.setPlantillas(new ArrayList<>());
            for (PlantillaRequestDTO plantilla : plantillasTyC) {
                boolean yaIncluida = request.getPlantillas().stream()
                    .anyMatch(p -> p.getIdPlantilla().equals(plantilla.getIdPlantilla()));
                if (!yaIncluida) {
                    request.getPlantillas().add(0, plantilla);
                }
            }
        }

        // **AHORA SÍ PROCESAR FIRMANTES MIXTOS CON VALIDACIONES ESTRICTAS**
        if ("REGISTRADO".equals(tipoUsuario)) {
            logger.info("👤 Procesando usuario REGISTRADO...");
            procesarFirmantesMixtos(request.getFirmantes(), request.getIdUsuario());
        } else if ("NO_REGISTRADO".equals(tipoUsuario)) {
            logger.info("🔄 Procesando usuario NO REGISTRADO...");
            // Para usuarios no registrados, el procesamiento se hace en procesarSolicitudFirmaInteresado
        }
        
        // LISTAS PARA ACUMULAR TODOS LOS DOCUMENTOS PROCESADOS
        List<Map<String, Object>> todosDocumentos = new ArrayList<>();
        List<Long> idsArchivosCreados = new ArrayList<>();
        List<String> nombresArchivos = new ArrayList<>();
        List<String> rutasCompletas = new ArrayList<>();
        int totalSolicitudes = 0;
        
        // **🔧 USAR EL MÉTODO ESPECIALIZADO PARA PLANTILLAS (CÓDIGO DUPLICADO ELIMINADO)**
        if (request.esPlantilla()) {
            logger.info("=== PROCESANDO MÚLTIPLES PLANTILLAS CON MÉTODO ESPECIALIZADO ===");
            Map<String, Object> resultadoPlantillas = procesarMultiplesPlantillasSinTokens(request, ipAddress);
            List<Map<String, Object>> documentosPlantillas = (List<Map<String, Object>>) resultadoPlantillas.get("todosDocumentos");
            if (documentosPlantillas != null) {
                todosDocumentos.addAll(documentosPlantillas);
            }
            List<Long> idsPlantillas = (List<Long>) resultadoPlantillas.get("idsArchivosCreados");
            if (idsPlantillas != null) {
                idsArchivosCreados.addAll(idsPlantillas);
                logger.info("✅ IDs de plantillas agregados: " + idsPlantillas);
            }
            List<String> nombresPlantillas = (List<String>) resultadoPlantillas.get("nombresArchivos");
            List<String> rutasPlantillas = (List<String>) resultadoPlantillas.get("rutasCompletas");
            if (nombresPlantillas != null) {
                nombresArchivos.addAll(nombresPlantillas);
            }
            if (rutasPlantillas != null) {
                rutasCompletas.addAll(rutasPlantillas);
            }
            Integer solicitudesPlantillas = (Integer) resultadoPlantillas.get("totalSolicitudesCreadas");
            if (solicitudesPlantillas != null) {
                totalSolicitudes += solicitudesPlantillas;
            }
            logger.info("✅ Total plantillas procesadas: " + request.getPlantillas().size());
            logger.info("✅ Total archivos creados de plantillas (incluye TyC individuales): " + idsPlantillas.size());
        }
        
        // PROCESAR DOCUMENTOS NUEVOS SI EXISTEN (SIN GENERAR TOKENS)
        if (request.esDocumentoNuevo()) {
            logger.info("=== PROCESANDO DOCUMENTOS NUEVOS SIN TOKENS ===");
            Map<String, Object> resultadoDocumentos = procesarDocumentosNuevosSinTokens(request, ipAddress);
            todosDocumentos.addAll((List<Map<String, Object>>) resultadoDocumentos.get("documentos"));
            totalSolicitudes += (Integer) resultadoDocumentos.get("totalSolicitudes");
            List<Long> idsDocumentosNuevos = (List<Long>) resultadoDocumentos.get("idsArchivosCreados");
            if (idsDocumentosNuevos != null) {
                idsArchivosCreados.addAll(idsDocumentosNuevos);
                List<Map<String, Object>> documentos = (List<Map<String, Object>>) resultadoDocumentos.get("documentos");
                for (Map<String, Object> doc : documentos) {
                    nombresArchivos.add((String) doc.get("nombreArchivo"));
                    rutasCompletas.add((String) doc.get("rutaCompleta"));
                }
            }
            logger.info("✅ Total archivos creados de documentos nuevos: " + idsDocumentosNuevos.size());
        }
        
        // GENERAR UN SOLO TOKEN POR FIRMANTE PARA TODOS LOS DOCUMENTOS
        logger.info("=== GENERANDO TOKENS ÚNICOS PARA TODOS LOS DOCUMENTOS ===");
        logger.info("Total documentos para tokens: " + idsArchivosCreados.size());
        logger.info("IDs de archivos: " + idsArchivosCreados);
        logger.info("Nombres de archivos: " + nombresArchivos);
        
        Map<String, String> tokensGenerados = new HashMap<>();
        if (!idsArchivosCreados.isEmpty()) {
            tokensGenerados = tokenGenerationService.generarTokensAgrupadosParaFirmantes(
                idsArchivosCreados,
                request.getIdUsuario(),
                request.getFirmantes(),
                request.getFechaVigencia(),
                nombresArchivos,
                rutasCompletas
            );
            // NO GUARDAR EL TOKEN EN SolicitudFirma, solo usar para notificación y respuesta
        }
        
        // CONSTRUIR RESPUESTA UNIFICADA
        Map<String, Object> resultado = new HashMap<>();
        resultado.put("success", true);
        resultado.put("message", construirMensajeRespuestaUnificado(request, todosDocumentos.size()));
        resultado.put("documentos", todosDocumentos);
        resultado.put("tipoOrden", request.getTipoOrden());
        resultado.put("tipoFirma", request.getTipoFirma());
        resultado.put("totalFirmantes", request.getFirmantes() != null ? request.getFirmantes().size() : 0);
        resultado.put("totalDocumentos", todosDocumentos.size());
        resultado.put("tokensGenerados", tokensGenerados.size());
        resultado.put("tokens", new ArrayList<>(tokensGenerados.values()));
        resultado.put("firmantesNotificados", tokensGenerados.keySet());
        resultado.put("idsArchivos", idsArchivosCreados);
        resultado.put("totalSolicitudes", totalSolicitudes);
        resultado.put("tieneePlantilla", request.esPlantilla());
        resultado.put("tieneDocumentosNuevos", request.esDocumentoNuevo());
        resultado.put("procesamiento", "UNIFICADO_TOKENS_AGRUPADOS");
        resultado.put("origenSolicitud", "ENDPOINT_UNIFICADO_MIXTO");
        resultado.put("tokenUnicoPorFirmante", true);
        
        logger.info("=== PROCESAMIENTO UNIFICADO COMPLETADO ===");
        logger.info("✅ ArchivoFirma creado con ID: " + (idsArchivosCreados.isEmpty() ? "N/A" : idsArchivosCreados.get(0)));
        logger.info("✅ Total de solicitudes de firma creadas: " + totalSolicitudes);
        logger.info("✅ Total tokens únicos generados: " + tokensGenerados.size());
        logger.info("✅ Correos enviados exitosamente a: " + tokensGenerados.keySet());
        
        return resultado;
        
    } catch (FirmaException e) {
        logger.severe("Error de firma: " + e.getMessage());
        throw e;
    } catch (Exception e) {
        logger.severe("Error inesperado: " + e.getMessage());
        e.printStackTrace();
        throw new FirmaException("Error procesando solicitud unificada: " + e.getMessage());
    }
}
    
/**
 * Procesa una plantilla SIN generar tokens - CORREGIDO PARA INCLUIR TODOS LOS IDS EN RESPUESTA
 */
private Map<String, Object> procesarPlantillaSinTokens(SolicitudFirmaUnificadaDTO request, String ipAddress) throws FirmaException {
    Map<String, Object> resultado = new HashMap<>();
    List<Map<String, Object>> solicitudesIndividuales = new ArrayList<>();
    List<Long> idsArchivosCreados = new ArrayList<>(); // **TODOS LOS IDS (TyC + NORMALES)**
    List<String> nombresArchivos = new ArrayList<>();
    List<String> rutasCompletas = new ArrayList<>();
    int totalSolicitudesCreadas = 0;

    try {
        logger.info("Procesando plantilla sin tokens - ID: " + request.getIdPlantilla());
        
        // 1. Obtener la plantilla
        PlantillaDocumento plantilla = plantillaRepository.findByIdAndActiva(request.getIdPlantilla());
        if (plantilla == null) {
            throw new FirmaException("Plantilla no encontrada o inactiva: " + request.getIdPlantilla());
        }
        
        logger.info("Plantilla encontrada: " + plantilla.getNombrePlantilla());
        
        // 2. **🎯 DETERMINAR SI ES PLANTILLA TyC**
        boolean esPlantillaTyC = esPlantillaTyC(plantilla.getIdPlantilla(), plantilla.getNombreArchivo());
        
        if (esPlantillaTyC) {
            logger.info("📋 Procesando plantilla TyC - CREANDO SOLICITUDES INDIVIDUALES por firmante");

            // Permitir solicitud a usuarios no registrados y a usuarios que no tengan el campo tyc=true
            for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
                boolean esRegistrado = verificarSiFirmanteEstaRegistradoSolo(firmante);
                boolean tieneTyC = false;
                if (esRegistrado) {
                    try {
                        // Usa el servicio de validación de usuario para obtener el usuario por email
                        Usuario usuario = usuarioValidationService.obtenerUsuarioPorEmail(firmante.getEmail());
                        if (usuario != null) {
                            tieneTyC = usuario.getFirmadoTyc();
                        }
                    } catch (Exception ex) {
                        logger.warning("Error consultando firmadoTyc para usuario: " + firmante.getEmail() + " - " + ex.getMessage());
                        tieneTyC = false;
                    }
                }
                // Permitir si NO es registrado o si es registrado pero no tiene tyc=true
                if (!esRegistrado || !tieneTyC) {
                    logger.info("🔄 Creando TyC individual para firmante: " + firmante.getEmail());

                    Map<String, Object> solicitudIndividual = crearSolicitudTyCIndividual(plantilla, request, firmante, ipAddress);
                    solicitudesIndividuales.add(solicitudIndividual);

                    Long idArchivoCreado = (Long) solicitudIndividual.get("idArchivo");
                    String nombreArchivo = (String) solicitudIndividual.get("nombreArchivo");
                    String rutaCompleta = (String) solicitudIndividual.get("rutaCompleta");

                    idsArchivosCreados.add(idArchivoCreado);
                    nombresArchivos.add(nombreArchivo);
                    rutasCompletas.add(rutaCompleta);

                    logger.info("✅ TyC individual agregado para tokens - ID: " + idArchivoCreado +
                            " - Firmante: " + firmante.getEmail());

                    totalSolicitudesCreadas++;
                } else {
                    logger.info("ℹ️ Firmante registrado con TyC=true EXCLUIDO de TyC individual: " + firmante.getEmail());
                }
            }

            logger.info("📋 Total TyC individuales para tokens: " + solicitudesIndividuales.size());
        } else {
            // PLANTILLA NORMAL - CREAR UNA SOLA SOLICITUD PARA TODOS
            logger.info("📄 Procesando plantilla normal - CREANDO SOLICITUD ÚNICA para todos los firmantes");
            
            String rutaRelativaBase = generarRutaRelativa(request.getIdUsuario());
            
            ArchivoFirma archivoFirma = crearArchivoFirmaDesdePlantillaSinTokensConFirmantes(
                plantilla, 
                request, 
                rutaRelativaBase, 
                ipAddress, 
                request.getFirmantes() // TODOS LOS FIRMANTES
            );
            
            List<SolicitudFirma> solicitudes = crearSolicitudesFirmaConOrden(
                archivoFirma.getIdArchivoFirma(),
                request.getIdUsuario(),
                request.getFirmantes(),
                request.getTipoOrden(),
                request.getFechaVigencia()
            );
            
            String rutaPrincipal = getRutaArchivos(true);
            String rutaCompleta = rutaPrincipal + rutaRelativaBase + plantilla.getNombreArchivo();
            
            // **AGREGAR DOCUMENTO NORMAL PARA TOKENS**
            idsArchivosCreados.add(archivoFirma.getIdArchivoFirma());
            nombresArchivos.add(plantilla.getNombreArchivo());
            rutasCompletas.add(rutaCompleta);
            
            totalSolicitudesCreadas = solicitudes.size();
            
            logger.info("✅ Plantilla normal agregada para tokens - ID: " + archivoFirma.getIdArchivoFirma());
        }
        
        // 6. Construir respuesta unificada
        resultado.put("idPlantilla", request.getIdPlantilla());
        resultado.put("nombrePlantilla", plantilla.getNombrePlantilla());
        resultado.put("esPlantillaTyC", esPlantillaTyC);
        resultado.put("solicitudesIndividuales", solicitudesIndividuales);
        resultado.put("totalSolicitudesIndividuales", solicitudesIndividuales.size());
        resultado.put("totalSolicitudesCreadas", totalSolicitudesCreadas);
        
        // **🎯 CLAVE: INCLUIR TODOS LOS IDS PARA TOKENS AGRUPADOS**
        resultado.put("idsArchivosCreados", idsArchivosCreados);  // **TyC + NORMALES**
        resultado.put("nombresArchivos", nombresArchivos);        // **TyC + NORMALES**
        resultado.put("rutasCompletas", rutasCompletas);          // **TyC + NORMALES**
        resultado.put("solicitudesCreadas", totalSolicitudesCreadas);
        
        logger.info("✅ Plantilla procesada - Tipo: " + (esPlantillaTyC ? "TyC individuales" : "Normal"));
        logger.info("📊 IDs para tokens: " + idsArchivosCreados);
        logger.info("📄 Nombres para tokens: " + nombresArchivos);
        
        return resultado;
        
    } catch (Exception e) {
        logger.severe("Error procesando plantilla sin tokens: " + e.getMessage());
        throw new FirmaException("Error procesando plantilla: " + e.getMessage());
    }
}

/**
 * Crea una solicitud TyC individual para un firmante específico
 */
private Map<String, Object> crearSolicitudTyCIndividual(PlantillaDocumento plantilla, 
                                                        SolicitudFirmaUnificadaDTO request, 
                                                        FirmanteOrdenDTO firmante, 
                                                        String ipAddress) throws FirmaException {
    try {
        logger.info("🔄 Creando solicitud TyC individual para: " + firmante.getEmail());
        
        String nuevaRutaRelativa = generarRutaRelativa(request.getIdUsuario());
        
        logger.info("📁 Ruta individual para " + firmante.getEmail() + ": " + nuevaRutaRelativa);
        
        // 2. Crear archivo individual para este firmante
        ArchivoFirma archivoFirmaIndividual = crearArchivoFirmaDesdePlantillaSinTokensConFirmantes(
            plantilla, 
            request, 
            nuevaRutaRelativa, 
            ipAddress, 
            Arrays.asList(firmante) // SOLO ESTE FIRMANTE
        );
        
        // 3. Crear solicitud de firma individual
        List<SolicitudFirma> solicitudes = crearSolicitudesFirmaConOrden(
            archivoFirmaIndividual.getIdArchivoFirma(),
            request.getIdUsuario(),
            Arrays.asList(firmante), // SOLO ESTE FIRMANTE
            request.getTipoOrden(),
            request.getFechaVigencia()
        );
        
        // 4. Construir ruta completa
        String rutaPrincipal = getRutaArchivos(true);
        String rutaCompleta = rutaPrincipal + nuevaRutaRelativa + plantilla.getNombreArchivo();
        
        // 5. Construir respuesta individual
        Map<String, Object> solicitudIndividual = new HashMap<>();
        solicitudIndividual.put("idArchivo", archivoFirmaIndividual.getIdArchivoFirma());
        solicitudIndividual.put("nombreArchivo", plantilla.getNombreArchivo());
        solicitudIndividual.put("hashArchivo", archivoFirmaIndividual.getHashArchivo());
        solicitudIndividual.put("rutaCompleta", rutaCompleta);
        solicitudIndividual.put("rutaRelativa", nuevaRutaRelativa);
        solicitudIndividual.put("solicitudesCreadas", solicitudes.size());
        solicitudIndividual.put("firmante", firmante.getEmail());
        solicitudIndividual.put("tipoFirma", archivoFirmaIndividual.getTipoFirma());
        solicitudIndividual.put("esIndividual", true);
        
        logger.info("✅ Solicitud TyC individual creada exitosamente para: " + firmante.getEmail() + 
                   " - ID: " + archivoFirmaIndividual.getIdArchivoFirma());
        
        return solicitudIndividual;
        
    } catch (Exception e) {
        logger.severe("Error creando solicitud TyC individual para " + firmante.getEmail() + ": " + e.getMessage());
        throw new FirmaException("Error creando solicitud TyC individual: " + e.getMessage());
    }
}
    
    /**
     * Procesa documentos nuevos SIN generar tokens (para agrupación posterior)
     */
    private Map<String, Object> procesarDocumentosNuevosSinTokens(SolicitudFirmaUnificadaDTO request, String ipAddress) throws FirmaException {
    
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> documentosProcesados = new ArrayList<>();
        List<Long> idsArchivosCreados = new ArrayList<>();  // AGREGAR ESTA LISTA
        int totalSolicitudes = 0;
        
        try {
            logger.info("Procesando documentos nuevos sin tokens - Cantidad: " + request.getDocumentos().size());
            
            // Crear request temporal para procesar documentos
            FirmaOrdenRequestDTO tempRequest = new FirmaOrdenRequestDTO();
            tempRequest.setTipoOrden(request.getTipoOrden());
            tempRequest.setFechaVigencia(request.getFechaVigencia());
            tempRequest.setTipoFirma(request.getTipoFirma());
            tempRequest.setFirmantes(request.getFirmantes());
            
            // Procesar cada documento sin generar tokens
            for (DocumentoNuevoDTO docNuevo : request.getDocumentos()) {
                
                // Convertir DocumentoNuevoDTO a DocumentoOrdenDTO
                DocumentoOrdenDTO documentoOrden = convertirDocumentoNuevo(docNuevo, request);
                
                // Procesar documento individual (sin tokens)
                Map<String, Object> resultadoDoc = procesarDocumentoSinTokens(documentoOrden, tempRequest, ipAddress);
                resultadoDoc.put("tipoSolicitud", "DOCUMENTO_NUEVO");
                resultadoDoc.put("origenSolicitud", "ENDPOINT_UNIFICADO_SIN_TOKENS");
                
                documentosProcesados.add(resultadoDoc);
                
                // **AGREGAR EL ID DEL ARCHIVO CREADO A LA LISTA**
                if (resultadoDoc.get("idArchivo") != null) {
                    Long idArchivo = (Long) resultadoDoc.get("idArchivo");
                    idsArchivosCreados.add(idArchivo);
                    logger.info("✅ ID de archivo agregado para tokens: " + idArchivo);
                }
                
                Integer solicitudes = (Integer) resultadoDoc.get("solicitudesCreadas");
                totalSolicitudes += (solicitudes != null ? solicitudes : 0);
            }
            
            response.put("documentos", documentosProcesados);
            response.put("totalDocumentos", request.getDocumentos().size());
            response.put("totalSolicitudes", totalSolicitudes);
            response.put("tipoSolicitud", "DOCUMENTOS_NUEVOS_SIN_TOKENS");
            
            // **AGREGAR LA LISTA DE IDS AL RESPONSE**
            response.put("idsArchivosCreados", idsArchivosCreados);
            
            logger.info("Documentos nuevos procesados sin tokens - Total: " + documentosProcesados.size());
            logger.info("✅ IDs de archivos para tokens: " + idsArchivosCreados);
            
            return response;
            
        } catch (Exception e) {
            logger.severe("Error procesando documentos nuevos sin tokens: " + e.getMessage());
            throw new FirmaException("Error procesando documentos nuevos: " + e.getMessage());
        }
    }

    /**
     * Construye mensaje de respuesta unificado
     */
    private String construirMensajeRespuestaUnificado(SolicitudFirmaUnificadaDTO request, int totalDocumentos) {
        if (request.esPlantilla() && request.esDocumentoNuevo()) {
            return "Solicitud unificada procesada exitosamente: " + totalDocumentos + " documento(s) (plantilla + archivos nuevos) con tokens únicos por firmante";
        } else if (request.esPlantilla()) {
            return "Plantilla procesada exitosamente con tokens únicos por firmante";
        } else {
            return "Documentos nuevos procesados exitosamente con tokens únicos por firmante";
        }
    }

    /**
     * Procesa un documento sin generar tokens (para agrupación posterior)
     */
    private Map<String, Object> procesarDocumentoSinTokens(DocumentoOrdenDTO documento, FirmaOrdenRequestDTO request, String ipAddress) throws FirmaException {
        Map<String, Object> resultado = new HashMap<>();
        
        try {
            logger.info("Procesando documento: " + documento.getNombreArchivo());
            
            // Generar ruta relativa
            String rutaRelativa = generarRutaRelativa(documento.getIdUsuario());
            
            // Obtener ruta principal y construir ruta completa
            String rutaPrincipal = getRutaArchivos(true);
            String rutaCompleta = rutaPrincipal + rutaRelativa + documento.getNombreArchivo();
            
            // Crear directorio
            String directorio = rutaPrincipal + rutaRelativa;
            Utilities.crearDirectorio(directorio, true);
            
            // Guardar archivo
            byte[] archivoBytes = Base64.getDecoder().decode(documento.getArchivo64());
            guardarArchivo(archivoBytes, rutaCompleta);
            
            // Generar hash
            String hashArchivo = Utilities.generarHash(rutaCompleta);
            
            // Verificar si ya existe un archivo con el mismo hash
            List<ArchivoFirma> archivosExistentes = archivoFirmaService.getArchivoFirmadoByHash(hashArchivo);
            if (!archivosExistentes.isEmpty()) {
                throw new FirmaException("Ya existe un archivo con el mismo contenido: " + documento.getNombreArchivo());
            }
            
            // Crear registro ArchivoFirma
            ArchivoFirma archivoFirma = archivoFirmaService.guardarArchivoFirma(
                documento.getNombreArchivo(),
                hashArchivo,
                documento.getCantidadFirmas(),
                1, // Estado pendiente
                ipAddress,
                documento.getIdUsuario(),
                rutaRelativa, // Solo la ruta relativa
                documento.getDescripcion()
            );
            
            // Configurar tipo de firma y emails de firmantes
            archivoFirma.setTipoFirma(request.getTipoFirma());
            
            // Crear cadena de emails de firmantes
            StringBuilder emailFirmantes = new StringBuilder();
            for (int i = 0; i < request.getFirmantes().size(); i++) {
                if (i > 0) emailFirmantes.append(",");
                emailFirmantes.append(request.getFirmantes().get(i).getEmail());
            }
            archivoFirma.setEmailFirmantes(emailFirmantes.toString());
            
            // Actualizar el archivo firma
            archivoFirmaService.update(archivoFirma);
            
            logger.info("ArchivoFirma creado con ID: " + archivoFirma.getIdArchivoFirma());
            
            // Crear solicitudes de firma con orden
            List<SolicitudFirma> solicitudes = crearSolicitudesFirmaConOrden(
                archivoFirma.getIdArchivoFirma(),
                documento.getIdUsuario(),
                request.getFirmantes(),
                request.getTipoOrden(),
                request.getFechaVigencia()
            );
            
            resultado.put("idArchivo", archivoFirma.getIdArchivoFirma());
            resultado.put("nombreArchivo", documento.getNombreArchivo());
            resultado.put("hashArchivo", hashArchivo);
            resultado.put("solicitudesCreadas", solicitudes.size());
            resultado.put("rutaRelativa", rutaRelativa);
            resultado.put("rutaCompleta", rutaCompleta);
            
            logger.info("Documento procesado exitosamente: " + documento.getNombreArchivo());
            
            return resultado;
            
        } catch (FirmaException e) {
            logger.severe("Error de firma procesando documento " + documento.getNombreArchivo() + ": " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.severe("Error inesperado procesando documento " + documento.getNombreArchivo() + ": " + e.getMessage());
            throw new FirmaException("Error procesando documento: " + e.getMessage());
        }
    }
    
    /**
 * Valida una solicitud unificada - ADAPTADA PARA USUARIOS NO REGISTRADOS
 */
private void validarSolicitudUnificada(SolicitudFirmaUnificadaDTO request) throws FirmaException {
    if (request == null) {
        throw new FirmaException("Request no puede ser null");
    }
    
    // **VALIDACIÓN ADAPTATIVA**: No requerir idUsuario si es usuario no registrado
    boolean esUsuarioNoRegistrado = detectarUsuarioNoRegistrado(request);
    
    if (!esUsuarioNoRegistrado && request.getIdUsuario() == null) {
        throw new FirmaException("ID de usuario es requerido para usuarios registrados");
    }
    
    if (request.getFirmantes() == null || request.getFirmantes().isEmpty()) {
        throw new FirmaException("La lista de firmantes no puede estar vacía");
    }
    
    // Validar que tenga al menos plantilla o documentos nuevos
    if (!request.esPlantilla() && !request.esDocumentoNuevo()) {
        throw new FirmaException("Debe especificar al menos una plantilla o documentos nuevos");
    }
    
    // APLICAR VALORES POR DEFECTO SI NO SE ESPECIFICARON
    if (request.getTipoOrden() == null || request.getTipoOrden().trim().isEmpty()) {
            request.setTipoOrden("PARALELO");
        }
        
        // Aplicar valores por defecto a firmantes
        for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
            if (firmante.getRol() == null || firmante.getRol().trim().isEmpty()) {
                firmante.setRol("Firmante");
            }
            if (firmante.getOrden() <= 0) {
                firmante.setOrden(1);
            }
        }
        
        if (!"PARALELO".equals(request.getTipoOrden()) && !"SECUENCIAL".equals(request.getTipoOrden())) {
            throw new FirmaException("Tipo de orden debe ser PARALELO o SECUENCIAL");
        }
        
        // AJUSTAR ÓRDENES AUTOMÁTICAMENTE PARA TIPO PARALELO
        if ("PARALELO".equals(request.getTipoOrden())) {
            // Para tipo PARALELO, asignar orden 1 a todos
            for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
                firmante.setOrden(1);
            }
            logger.info("Tipo PARALELO: Todos los firmantes asignados con orden 1");
        } else {
            // Para tipo SECUENCIAL, validar órdenes únicos y consecutivos
            Set<Integer> ordenes = new HashSet<>();
            for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
                if (!ordenes.add(firmante.getOrden())) {
                    throw new FirmaException("Los órdenes de firma deben ser únicos para tipo SECUENCIAL");
                }
            }
            
            // Verificar que los órdenes sean consecutivos desde 1
            List<Integer> ordenesOrdenados = new ArrayList<>(ordenes);
            Collections.sort(ordenesOrdenados);
            for (int i = 0; i < ordenesOrdenados.size(); i++) {
                if (ordenesOrdenados.get(i) != i + 1) {
                    throw new FirmaException("Los órdenes de firma deben ser consecutivos comenzando desde 1 para tipo SECUENCIAL");
                }
            }
        }
        
        // Validar emails únicos
        Set<String> emails = new HashSet<>();
        for (FirmanteOrdenDTO firmante : request.getFirmantes()) {
            if (firmante.getEmail() == null || firmante.getEmail().trim().isEmpty()) {
                throw new FirmaException("Todos los firmantes deben tener email");
            }
            if (!emails.add(firmante.getEmail().toLowerCase())) {
                throw new FirmaException("Los emails de los firmantes deben ser únicos");
            }
        }
        
        // Validar plantilla si se especifica
        if (request.esPlantilla()) {
            if (request.getIdPlantilla() == null) {
                throw new FirmaException("ID de plantilla es requerido cuando se especifica plantilla");
            }
        }
        
        // Validar documentos nuevos si se especifican
        if (request.esDocumentoNuevo()) {
            if (request.getDocumentos() == null || request.getDocumentos().isEmpty()) {
                throw new FirmaException("Lista de documentos no puede estar vacía cuando se especifican documentos nuevos");
            }
            
            for (DocumentoNuevoDTO documento : request.getDocumentos()) {
                if (documento.getNombreArchivo() == null || documento.getNombreArchivo().trim().isEmpty()) {
                    throw new FirmaException("Nombre de archivo es requerido para todos los documentos");
                }
                if (documento.getArchivo64() == null || documento.getArchivo64().trim().isEmpty()) {
                    throw new FirmaException("Contenido del archivo es requerido para todos los documentos");
                }
            }
        }
    logger.info("Validación unificada completada - Tipo usuario: " + (esUsuarioNoRegistrado ? "NO_REGISTRADO" : "REGISTRADO"));
}

/**
 * Detecta si la solicitud corresponde a un usuario no registrado (interesado) - CORREGIDO
 */
private boolean detectarUsuarioNoRegistrado(SolicitudFirmaUnificadaDTO request) {
    if (request == null) return false;
    
    logger.info("🔍 DETECTANDO TIPO DE USUARIO:");
    logger.info("   - idUsuario: " + request.getIdUsuario());
    logger.info("   - nombreInteresado: '" + request.getNombreInteresado() + "'");
    logger.info("   - emailInteresado: '" + request.getEmailInteresado() + "'");

    // ✅ DEBE TENER DATOS COMPLETOS DE INTERESADO Y NO TENER ID USUARIO
    boolean tieneDatosInteresado = 
        (request.getNombreInteresado() != null && !request.getNombreInteresado().trim().isEmpty()) &&
        (request.getEmailInteresado() != null && !request.getEmailInteresado().trim().isEmpty()) &&
        (request.getTelefonoInteresado() != null && !request.getTelefonoInteresado().trim().isEmpty()) &&
        (request.getTipoDocumentoInteresado() != null && !request.getTipoDocumentoInteresado().trim().isEmpty()) &&
        (request.getNumeroDocumentoInteresado() != null && !request.getNumeroDocumentoInteresado().trim().isEmpty());
    
    boolean noTieneIdUsuario = (request.getIdUsuario() == null || request.getIdUsuario() <= 0);
    
    boolean esNoRegistrado = tieneDatosInteresado && noTieneIdUsuario;
    
    logger.info("🔍 Resultado detección:");
    logger.info("   - Tiene datos interesado completos: " + tieneDatosInteresado);
    logger.info("   - No tiene ID usuario: " + noTieneIdUsuario);
    logger.info("   - Es NO registrado: " + esNoRegistrado);
    
    return esNoRegistrado;
}

/**
 * Convierte DocumentoNuevoDTO a DocumentoOrdenDTO
 */
private DocumentoOrdenDTO convertirDocumentoNuevo(DocumentoNuevoDTO docNuevo, SolicitudFirmaUnificadaDTO request) {
    DocumentoOrdenDTO documentoOrden = new DocumentoOrdenDTO();
    documentoOrden.setNombreArchivo(docNuevo.getNombreArchivo());
    documentoOrden.setArchivo64(docNuevo.getArchivo64());
    documentoOrden.setDescripcion(docNuevo.getDescripcion());
    documentoOrden.setIdUsuario(request.getIdUsuario());
    documentoOrden.setCantidadFirmas(request.getFirmantes().size());
    
    return documentoOrden;
}

/**
 * Genera hash único usando UID temporal (no persistido)
 * El UID se usa solo para hacer el hash único, luego se descarta
 */
private String generarHashUnicoConUID(String rutaArchivo, String uid) throws FirmaException {
    try {
        // 1. GENERAR HASH DEL ARCHIVO COPIADO (usando método de Utilities)
        String hashArchivo = Utilities.generarHash(rutaArchivo);
        
        // 2. COMBINAR HASH DEL ARCHIVO CON UID TEMPORAL
        String combinacion = hashArchivo + uid;
        
        // 3. GENERAR HASH DE LA COMBINACIÓN PARA CREAR HASH ÚNICO
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(combinacion.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            String hashUnico = hexString.toString();
            
            logger.info("Hash archivo original: " + hashArchivo);
            logger.info("UID temporal: " + uid);
            logger.info("Hash único generado: " + hashUnico);
            
            return hashUnico;
            
        } catch (Exception hashEx) {
            // Fallback: usar timestamp + hash original si falla la generación
            String hashUnico = hashArchivo + "_" + System.currentTimeMillis();
            logger.info("Usando hash fallback: " + hashUnico);
            return hashUnico;
        }
        
    } catch (Exception e) {
        logger.severe("Error generando hash único - Ruta: " + rutaArchivo + ", UID: " + uid + ", Error: " + e.getMessage());
        throw new FirmaException("Error generando hash único: " + e.getMessage());
    }
}

public void generarTokensSiguienteOrden(Long idArchivoFirma, int ordenCompletado) throws FirmaException {
    try {
        ArchivoFirma archivoFirma = archivoFirmaService.findById(idArchivoFirma);
        if (archivoFirma == null) {
            throw new FirmaException("Archivo no encontrado: " + idArchivoFirma);
        }

        // Buscar solicitudes del siguiente orden
        List<SolicitudFirma> solicitudesSiguienteOrden = solicitudFirmaService.findByIdArchivoFirmaAndOrdenFirma(idArchivoFirma, ordenCompletado + 1);
        if (solicitudesSiguienteOrden == null || solicitudesSiguienteOrden.isEmpty()) {
            logger.info("🏁 NO hay firmantes para orden " + (ordenCompletado + 1) + " - PROCESO SECUENCIAL COMPLETADO");
            return;
        }

        List<FirmanteOrdenDTO> firmantesOrden = new ArrayList<>();
        for (SolicitudFirma solicitud : solicitudesSiguienteOrden) {
            FirmanteOrdenDTO firmante = new FirmanteOrdenDTO();
            firmante.setEmail(solicitud.getEmailFirmante());
            firmante.setOrden(solicitud.getOrdenFirma());
            firmante.setRol(solicitud.getRolFirmante() != null ? solicitud.getRolFirmante() : "Firmante");
            firmantesOrden.add(firmante);
        }

        for (FirmanteOrdenDTO firmante : firmantesOrden) {
            List<Long> idsArchivos = new ArrayList<>();
            List<String> nombresArchivos = new ArrayList<>();
            List<String> rutasCompletas = new ArrayList<>();

            // Agregar el archivo principal
            idsArchivos.add(idArchivoFirma);
            nombresArchivos.add(archivoFirma.getNombreArchivo());
            String rutaCompleta = getRutaArchivos(true) + archivoFirma.getRutaRelativaArchivo() + archivoFirma.getNombreArchivo();
            rutasCompletas.add(rutaCompleta);

            // Agregar TyC individuales si existen para este firmante
            List<ArchivoFirma> tycIndividuales = archivoFirmaService.findArchivosTyCIndividualesPorEmail(firmante.getEmail(), archivoFirma.getIdUsuario());
            if (tycIndividuales != null && !tycIndividuales.isEmpty()) {
                for (ArchivoFirma tyc : tycIndividuales) {
                    idsArchivos.add(tyc.getIdArchivoFirma());
                    nombresArchivos.add(tyc.getNombreArchivo());
                    rutasCompletas.add(getRutaArchivos(true) + tyc.getRutaRelativaArchivo() + tyc.getNombreArchivo());
                }
            }

            // Ordenar los IDs para asegurar consistencia en la búsqueda y generación del token
            Collections.sort(idsArchivos);

            // Buscar el token usando patrón LIKE para cualquier coincidencia de los IDs principales
            Token tokenEntity = tokenService.buscarTokenPorEmailYIdArchivo(firmante.getEmail(), idArchivoFirma);
            String token = (tokenEntity != null && tokenEntity.getIdToken() != null) ? tokenEntity.getIdToken() : null;

            // Si el token existe, reconstruir los adjuntos desde los IDs del token
            if (tokenEntity != null && tokenEntity.getIdToken() != null && tokenEntity.getIds() != null) {
                nombresArchivos.clear();
                rutasCompletas.clear();
                List<Long> idsAdjuntos = new ArrayList<>();
                String idsStr = tokenEntity.getIds();
                if (idsStr != null && !idsStr.isEmpty()) {
                    String[] partes = idsStr.replace("0-", "").replace("-0", "").split("-");
                    for (String parte : partes) {
                        try {
                            idsAdjuntos.add(Long.parseLong(parte));
                        } catch (NumberFormatException ignore) {}
                    }
                }
                for (Long idAdjunto : idsAdjuntos) {
                    ArchivoFirma adjunto = archivoFirmaService.findById(idAdjunto);
                    if (adjunto != null) {
                        nombresArchivos.add(adjunto.getNombreArchivo());
                        rutasCompletas.add(getRutaArchivos(true) + adjunto.getRutaRelativaArchivo() + adjunto.getNombreArchivo());
                    }
                }
                logger.info("Adjuntos reconstruidos para correo: " + nombresArchivos);
            }
            // **CORREGIDO: MEJORAR EXTRACCIÓN DE DATOS PARA EL CORREO CON MEJOR LOGGING**
            DatosEmailMultipleFirmaDTO datosEmail;
            try {
                logger.info("🔍 Extrayendo datos para correo secuencial:");
                logger.info("   - ID Usuario (remitente): " + archivoFirma.getIdUsuario());
                logger.info("   - Email firmante: " + firmante.getEmail());
                logger.info("   - Nombre firmante: " + firmante.getNombreCompleto());
                logger.info("   - Documentos: " + nombresArchivos);

                datosEmail = emailDataHelper.extraerDatos(
                    archivoFirma.getIdUsuario(),    // ID del usuario remitente original
                    firmante.getEmail(),            // Email del firmante actual
                    firmante.getNombreCompleto(),   // Nombre del firmante (puede ser null)
                    nombresArchivos                 // Lista de nombres de documentos
                );

                logger.info("✅ Datos extraídos exitosamente para orden secuencial:");
                logger.info("   - Remitente: " + datosEmail.getNombreRemitente() + " (" + datosEmail.getEmailRemitente() + ")");
                logger.info("   - Firmante: " + datosEmail.getNombreFirmante());
                logger.info("   - Documentos: " + datosEmail.getNombreDocumento());

            } catch (Exception e) {
                logger.severe("❌ ERROR CRÍTICO extrayendo datos del email para orden secuencial:");
                logger.severe("   - Firmante: " + firmante.getEmail());
                logger.severe("   - Error: " + e.getMessage());
                e.printStackTrace();

                // **CREAR DATOS DE FALLBACK PARA EVITAR PLANTILLA GENÉRICA**
                try {
                    logger.warning("⚠️ Intentando crear datos de fallback...");
                    datosEmail = new DatosEmailMultipleFirmaDTO();
                    datosEmail.setNombreDocumento(nombresArchivos);
                    datosEmail.setNombreFirmante(firmante.getEmail()); // Usar email como nombre si falla
                    datosEmail.setNombreRemitente("Usuario del Sistema"); // Fallback genérico
                    datosEmail.setEmailRemitente("<EMAIL>"); // Email genérico
                    logger.warning("⚠️ Datos de fallback creados para evitar error total");
                } catch (Exception fallbackEx) {
                    logger.severe("❌ ERROR TOTAL: No se pudieron crear ni datos normales ni de fallback");
                    fallbackEx.printStackTrace();
                    continue; // Saltar este firmante completamente
                }
            }
                  
            // Si el token aún no existe, generarlo igual que ms-firma
            if (token == null || token.isEmpty()) {
                // CORREGIDO: Filtrar nulos y vacíos para evitar tokens como 0--939--0
                String idsToken = "0-" + idsArchivos.stream()
                        .filter(Objects::nonNull)
                        .map(String::valueOf)
                        .filter(s -> !s.trim().isEmpty())
                        .collect(Collectors.joining("-")) + "-0";
                token = tokenService.crearTokenFirmante(
                    archivoFirma.getIdUsuario(),
                    idsToken,
                    firmante.getEmail(),
                    null // Puedes agregar fecha de vigencia si es necesario
                );
                logger.info("Token generado para firmante: " + firmante.getEmail() + " con ids: " + idsToken);
            } else {
                logger.info("Token encontrado por patrón LIKE para firmante: " + firmante.getEmail() + " con ids: " + tokenEntity.getIds());
            }

            if (token != null && !token.isEmpty()) {
  
                boolean enviado = emailNotificationService.enviarNotificacionAgrupada(
                    token, firmante.getEmail(), nombresArchivos, rutasCompletas, datosEmail
                );
                if (enviado) {
                    logger.info("Correo enviado a: " + firmante.getEmail() + " con token: " + token);
                } else {
                    logger.warning("Error enviando correo a: " + firmante.getEmail() + " con token: " + token);
                }
            } else {
                logger.warning("No se pudo generar token para firmante: " + firmante.getEmail());
            }
        }

        logger.info("🎉 === TOKENS PARA ORDEN " + (ordenCompletado + 1) + " ENVIADOS EXITOSAMENTE ===");

    } catch (Exception e) {
        logger.severe("❌ ERROR CRÍTICO generando tokens para siguiente orden: " + e.getMessage());
        throw new FirmaException("Error generando tokens para siguiente orden: " + e.getMessage());
    }
}

/**
 * Procesa solicitud de firma para usuario interesado (no registrado) - CORREGIDO PARA USUARIOS PERMANENTES
 */
public Map<String, Object> procesarSolicitudFirmaInteresado(SolicitudFirmaUnificadaDTO request, String ipAddress) throws FirmaException {
    try {
        logger.info("=== PROCESANDO SOLICITUD DE USUARIO INTERESADO (PERMANENTE) ===");
        logger.info("Interesado: " + request.getNombreInteresado());
        logger.info("Email: " + request.getEmailInteresado());
        
        // 1. Validar que es solicitud de interesado
        validarSolicitudInteresado(request);
        
        // 2. ✅ CREAR USUARIO PERMANENTE (NO TEMPORAL)
        Long idUsuarioPermanente = crearOObtenerUsuarioPermanente(request);
        
        // 3. Asignar el ID de usuario permanente al request
        request.setIdUsuario(idUsuarioPermanente);
        
        // 4. Procesar con la lógica unificada existente
        Map<String, Object> resultado = procesarSolicitudUnificada(request, ipAddress);
        
        // 5. Agregar información específica de interesado
        resultado.put("tipoUsuario", "INTERESADO_REGISTRADO_PERMANENTE");
        resultado.put("nombreInteresado", request.getNombreInteresado());
        resultado.put("emailInteresado", request.getEmailInteresado());
        resultado.put("idUsuarioPermanente", idUsuarioPermanente);
        resultado.put("procesamientoEspecial", "USUARIO_REGISTRADO_AUTOMATICAMENTE");
        
        logger.info("✅ Solicitud de interesado procesada exitosamente con usuario permanente ID: " + idUsuarioPermanente);
        
        return resultado;
        
    } catch (Exception e) {
        logger.severe("Error procesando solicitud de interesado: " + e.getMessage());
        throw new FirmaException("Error procesando solicitud de interesado: " + e.getMessage());
    }
}

/**
 * Valida solicitud de usuario interesado - MANTENER ESTE MÉTODO
 */
private void validarSolicitudInteresado(SolicitudFirmaUnificadaDTO request) throws FirmaException {
    if (request == null) {
        throw new FirmaException("Request no puede ser null");
    }
    
    // Validar datos del interesado
    if (request.getNombreInteresado() == null || request.getNombreInteresado().trim().isEmpty()) {
        throw new FirmaException("Nombre del interesado es requerido");
    }
    
    if (request.getEmailInteresado() == null || request.getEmailInteresado().trim().isEmpty()) {
        throw new FirmaException("Email del interesado es requerido");
    }
    if (!request.getEmailInteresado().matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
        throw new FirmaException("Formato de email inválido");
    }
    
    if (request.getTelefonoInteresado() == null || request.getTelefonoInteresado().trim().isEmpty()) {
        throw new FirmaException("Teléfono del interesado es requerido");
    }
    
    if (request.getTipoDocumentoInteresado() == null || request.getTipoDocumentoInteresado().trim().isEmpty()) {
        throw new FirmaException("Tipo de documento del interesado es requerido");
    }
    
    if (request.getNumeroDocumentoInteresado() == null || request.getNumeroDocumentoInteresado().trim().isEmpty()) {
        throw new FirmaException("Número de documento del interesado es requerido");
    }
}

/**
 * Crea o obtiene usuario PERMANENTE (no temporal) - CORREGIDO CON REFERIDO Y BCRYPT
 */
private Long crearOObtenerUsuarioPermanente(SolicitudFirmaUnificadaDTO request) throws FirmaException {
    Usuario usuario = usuarioValidationService.crearOObtenerUsuarioPermanente(request);
    if (usuario == null || usuario.getIdUsuario() == null) {
        throw new FirmaException("No se pudo crear u obtener el usuario permanente");
    }
    return usuario.getIdUsuario();
}

/**
 * Procesa firmantes mixtos detectando automáticamente si están registrados - SIN CAMPO esUsuarioRegistrado
 */
public void procesarFirmantesMixtos(List<FirmanteOrdenDTO> firmantes, Long idUsuarioSolicitante) throws FirmaException {
    usuarioValidationService.procesarFirmantesMixtos(firmantes, idUsuarioSolicitante);
}

/**
 * Valida datos de firmante no registrado - MÉTODO PÚBLICO
 */
public void validarDatosFirmanteNoRegistrado(FirmanteOrdenDTO firmante) throws FirmaException {
    usuarioValidationService.validarDatosFirmanteNoRegistrado(firmante);
}

/**
 * Agrega automáticamente plantillas TyC para usuarios no registrados
 */
private void agregarPlantillasTyCAutomaticamente(SolicitudFirmaUnificadaDTO request) {
    try {
        logger.info("🎯 AGREGANDO PLANTILLAS TyC AUTOMÁTICAMENTE...");
        
        // IDs de las plantillas TyC confirmados
        Long idPlantillaTyC = 1L;              // ID de la plantilla "Términos y Condiciones"
        Long idPlantillaDatos = 2L;            // ID de la plantilla "Autorización Datos Personales"
        
        logger.info("📋 Buscando plantillas TyC con IDs:");
        logger.info("   - TyC ID: " + idPlantillaTyC);
        logger.info("   - Datos ID: " + idPlantillaDatos);
        
        // Verificar que las plantillas existan - MEJORADO
        PlantillaDocumento plantillaTyC = null;
        PlantillaDocumento plantillaDatos = null;
        
        try {
            Optional<PlantillaDocumento> plantillaTyCOpt = plantillaRepository.findById(idPlantillaTyC);
            Optional<PlantillaDocumento> plantillaDatosOpt = plantillaRepository.findById(idPlantillaDatos);
            
            if (plantillaTyCOpt.isPresent()) {
                plantillaTyC = plantillaTyCOpt.get();
                logger.info("✅ TyC encontrada: " + plantillaTyC.getNombreArchivo());
            } else {
                logger.warning("❌ Plantilla TyC no encontrada con ID: " + idPlantillaTyC);
            }
            
            if (plantillaDatosOpt.isPresent()) {
                plantillaDatos = plantillaDatosOpt.get();
                logger.info("✅ Datos encontrada: " + plantillaDatos.getNombreArchivo());
            } else {
                logger.warning("❌ Plantilla Datos no encontrada con ID: " + idPlantillaDatos);
            }
            
            // Si no se encuentran las plantillas, buscar por nombre como fallback
            if (plantillaTyC == null || plantillaDatos == null) {
                logger.info("🔍 Intentando buscar plantillas por nombre...");
                buscarPlantillasPorNombreFallback(request, plantillaTyC, plantillaDatos);
                return;
            }
            
        } catch (Exception e) {
            logger.warning("⚠️ Error verificando plantillas TyC: " + e.getMessage());
            e.printStackTrace();
            return;
        }
        
        // Crear lista de plantillas si no existe
        if (request.getPlantillas() == null) {
            request.setPlantillas(new ArrayList<>());
            logger.info("📝 Lista de plantillas creada desde cero");
        }
        
        logger.info("📋 Plantillas actuales en request: " + request.getPlantillas().size());
        for (PlantillaRequestDTO p : request.getPlantillas()) {
            logger.info("   - Plantilla existente ID: " + p.getIdPlantilla() + " - " + p.getDescripcion());
        }
        
        // Verificar que no estén ya incluidas
        boolean tieneTyC = false;
        boolean tieneDatos = false;
        
        for (PlantillaRequestDTO plantilla : request.getPlantillas()) {
            if (plantilla.getIdPlantilla().equals(idPlantillaTyC)) {
                tieneTyC = true;
                logger.info("🔍 TyC ya incluida en request");
            }
            if (plantilla.getIdPlantilla().equals(idPlantillaDatos)) {
                tieneDatos = true;
                logger.info("🔍 Datos ya incluida en request");
            }
        }
        
        // Agregar TyC si no está incluida
        if (!tieneTyC && plantillaTyC != null) {
            PlantillaRequestDTO plantillaTyCRequest = new PlantillaRequestDTO();
            plantillaTyCRequest.setIdPlantilla(idPlantillaTyC);
            plantillaTyCRequest.setDescripcion("Términos y Condiciones (agregado automáticamente)");
            request.getPlantillas().add(0, plantillaTyCRequest);
            logger.info("✅ Plantilla TyC agregada automáticamente - ID: " + idPlantillaTyC);
        } else if (tieneTyC) {
            logger.info("ℹ️ Plantilla TyC ya estaba incluida");
        } else {
            logger.warning("⚠️ No se pudo agregar TyC - plantilla no encontrada");
        }
        
        // Agregar Datos Personales si no está incluida
        if (!tieneDatos && plantillaDatos != null) {
            PlantillaRequestDTO plantillaDatosRequest = new PlantillaRequestDTO();
            plantillaDatosRequest.setIdPlantilla(idPlantillaDatos);
            plantillaDatosRequest.setDescripcion("Autorización Datos Personales (agregado automáticamente)");
            request.getPlantillas().add(plantillaDatosRequest); // Agregar al final
            logger.info("✅ Plantilla Datos Personales agregada automáticamente - ID: " + idPlantillaDatos);
        } else if (tieneDatos) {
            logger.info("ℹ️ Plantilla Datos Personales ya estaba incluida");
        } else {
            logger.warning("⚠️ No se pudo agregar Datos - plantilla no encontrada");
        }
        
        logger.info("🎯 PLANTILLAS TyC AGREGADAS EXITOSAMENTE");
        logger.info("📋 Total plantillas ahora: " + request.getPlantillas().size());
        
        // Mostrar todas las plantillas finales
        logger.info("📋 Lista final de plantillas:");
        for (int i = 0; i < request.getPlantillas().size(); i++) {
            PlantillaRequestDTO p = request.getPlantillas().get(i);
            logger.info("   " + (i + 1) + ". ID: " + p.getIdPlantilla() + " - " + p.getDescripcion());
        }
        
    } catch (Exception e) {
        logger.warning("⚠️ Error agregando plantillas TyC automáticamente: " + e.getMessage());
        e.printStackTrace();
        // No lanzar excepción para no interrumpir el flujo principal
    }
}

/**
 * Busca plantillas TyC por nombre como fallback
 */
private void buscarPlantillasPorNombreFallback(SolicitudFirmaUnificadaDTO request, 
                                               PlantillaDocumento plantillaTyCExistente, 
                                               PlantillaDocumento plantillaDatosExistente) {
    try {
        logger.info("🔍 Buscando plantillas TyC por nombre como fallback...");
        
        // Buscar todas las plantillas disponibles
        List<PlantillaDocumento> todasPlantillas = plantillaRepository.findAll();
        logger.info("📋 Total plantillas en BD: " + todasPlantillas.size());
        
        PlantillaDocumento plantillaTyC = plantillaTyCExistente;
        PlantillaDocumento plantillaDatos = plantillaDatosExistente;
        
        // Solo buscar las que faltan
        for (PlantillaDocumento plantilla : todasPlantillas) {
            String nombre = plantilla.getNombreArchivo() != null ? 
                           plantilla.getNombreArchivo().toLowerCase() : "";
            String descripcion = plantilla.getDescripcion() != null ? 
                               plantilla.getDescripcion().toLowerCase() : "";
            
            logger.info("📄 Evaluando plantilla ID " + plantilla.getIdPlantilla() + ": " + nombre);
            
            // Buscar TyC por nombre (solo si no se encontró por ID)
            if (plantillaTyC == null && 
                ((nombre.contains("terminos") && nombre.contains("condiciones")) ||
                 (nombre.contains("tyc")) ||
                 (descripcion.contains("términos") && descripcion.contains("condiciones")) ||
                 (descripcion.contains("tyc")))) {
                plantillaTyC = plantilla;
                logger.info("✅ TyC encontrada por nombre: " + plantilla.getNombreArchivo() + " (ID: " + plantilla.getIdPlantilla() + ")");
            }
            
            // Buscar Datos por nombre (solo si no se encontró por ID)
            if (plantillaDatos == null && 
                ((nombre.contains("datos") && nombre.contains("personales")) ||
                 (nombre.contains("autorizacion") && nombre.contains("datos")) ||
                 (descripcion.contains("datos") && descripcion.contains("personales")) ||
                 (descripcion.contains("autorización") && descripcion.contains("datos")))) {
                plantillaDatos = plantilla;
                logger.info("✅ Datos encontrada por nombre: " + plantilla.getNombreArchivo() + " (ID: " + plantilla.getIdPlantilla() + ")");
            }
        }
        
        if (plantillaTyC == null && plantillaDatos == null) {
            logger.warning("❌ No se encontraron plantillas TyC ni por ID ni por nombre");
            return;
        }
        
        // Crear lista de plantillas si no existe
        if (request.getPlantillas() == null) {
            request.setPlantillas(new ArrayList<>());
        }
        
        // Agregar las encontradas
        if (plantillaTyC != null) {
            PlantillaRequestDTO plantillaTyCRequest = new PlantillaRequestDTO();
            plantillaTyCRequest.setIdPlantilla(plantillaTyC.getIdPlantilla());
            plantillaTyCRequest.setDescripcion("Términos y Condiciones (encontrado por " + (plantillaTyCExistente != null ? "ID" : "nombre") + ")");
            request.getPlantillas().add(0, plantillaTyCRequest);
            logger.info("✅ TyC agregada - ID: " + plantillaTyC.getIdPlantilla());
        }
        
        if (plantillaDatos != null) {
            PlantillaRequestDTO plantillaDatosRequest = new PlantillaRequestDTO();
            plantillaDatosRequest.setIdPlantilla(plantillaDatos.getIdPlantilla());
            plantillaDatosRequest.setDescripcion("Autorización Datos Personales (encontrado por " + (plantillaDatosExistente != null ? "ID" : "nombre") + ")");
            request.getPlantillas().add(plantillaDatosRequest);
            logger.info("✅ Datos agregada - ID: " + plantillaDatos.getIdPlantilla());
        }
        
        logger.info("🎯 Fallback completado - Total plantillas: " + request.getPlantillas().size());
        
    } catch (Exception e) {
        logger.warning("❌ Error en búsqueda fallback de plantillas: " + e.getMessage());
        e.printStackTrace();
    }
}
/**
 * Verifica si hay firmantes no registrados en la lista - CORREGIDO
 */
private boolean verificarSiHayFirmantesNoRegistrados(List<FirmanteOrdenDTO> firmantes) {
    return usuarioValidationService.verificarSiHayFirmantesNoRegistrados(firmantes);
}

/**
 * Verifica ÚNICAMENTE si un firmante está registrado (sin validaciones estrictas de datos)
 * Solo para determinar si agregar plantillas TyC, no para validar conflictos
 */
private boolean verificarSiFirmanteEstaRegistradoSolo(FirmanteOrdenDTO firmante) {
    return usuarioValidationService.verificarSiFirmanteEstaRegistradoSolo(firmante);
}

public boolean isFirmanteRegistrado(FirmanteOrdenDTO firmante) {
    return this.verificarSiFirmanteEstaRegistradoSolo(firmante);
}


/**
 * Crea ArchivoFirma desde plantilla SIN generar tokens - CON FIRMANTES FILTRADOS COMO PARÁMETRO
 */
private ArchivoFirma crearArchivoFirmaDesdePlantillaSinTokensConFirmantes(PlantillaDocumento plantilla, 
    SolicitudFirmaUnificadaDTO request, 
    String nuevaRutaRelativa, 
    String ipAddress,
    List<FirmanteOrdenDTO> firmantesParaEstaPlantilla) throws FirmaException {

    try {
        logger.info("Creando ArchivoFirma desde plantilla (sin tokens) con firmantes específicos");

        // 1. OBTENER RUTA DE LA PLANTILLA
        String rutaBasePlantillas = env.getProperty("routes.custom.file", "/opt/firmese/files") + "/plantillas/";
        String rutaCompletaPlantilla = rutaBasePlantillas + plantilla.getRutaRelativaArchivo();

        logger.info("Ruta de plantilla original: " + rutaCompletaPlantilla);

        // Verificar que el archivo de plantilla existe
        File archivoPlantilla = new File(rutaCompletaPlantilla);
        if (!archivoPlantilla.exists()) {
            throw new FirmaException("Archivo de plantilla no encontrado: " + rutaCompletaPlantilla);
        }

        // 2. CREAR RUTA DESTINO PARA LA COPIA
        String rutaPrincipal = getRutaArchivos(true);
        String rutaCompleta = rutaPrincipal + nuevaRutaRelativa + plantilla.getNombreArchivo();

        logger.info("Ruta destino para copia: " + rutaCompleta);

        // 3. CREAR DIRECTORIO DESTINO Y COPIAR ARCHIVO
        String nuevoDirectorio = rutaPrincipal + nuevaRutaRelativa;
        Utilities.crearDirectorio(nuevoDirectorio, true);

        // Si el archivo destino existe, eliminarlo antes de copiar
        File archivoDestino = new File(rutaCompleta);
        if (archivoDestino.exists()) {
            boolean deleted = archivoDestino.delete();
            if (!deleted) {
                logger.warning("No se pudo eliminar archivo destino antes de copiar: " + rutaCompleta);
            }
        }

        // Usar try-with-resources para asegurar cierre de streams
        try {
            Files.copy(archivoPlantilla.toPath(), Paths.get(rutaCompleta), StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            logger.severe("Error copiando archivo de plantilla: " + e.getMessage());
            throw new FirmaException("Error copiando archivo de plantilla: " + e.getMessage());
        }

        logger.info("Archivo copiado exitosamente: " + rutaCompletaPlantilla + " -> " + rutaCompleta);

        // ...resto del método sin cambios...
        // 4. GENERAR HASH ÚNICO
        String uidTemporal = UUID.randomUUID().toString();
        String hashUnico = generarHashUnicoConUID(rutaCompleta, uidTemporal);

        // 5. USAR DESCRIPCIÓN PERSONALIZADA
        String descripcion = (request.getDescripcionPersonalizada() != null && !request.getDescripcionPersonalizada().trim().isEmpty()) 
            ? request.getDescripcionPersonalizada() 
            : plantilla.getDescripcion();

        int cantidadFirmasEsperada = firmantesParaEstaPlantilla.size();
        boolean esPlantillaTyC = esPlantillaTyC(plantilla.getIdPlantilla(), plantilla.getNombreArchivo());

        ArchivoFirma archivoFirma = archivoFirmaService.guardarArchivoFirma(
            plantilla.getNombreArchivo(),
            hashUnico,
            cantidadFirmasEsperada,
            1,
            ipAddress,
            request.getIdUsuario(),
            nuevaRutaRelativa,
            descripcion
        );

        String tipoFirma = "MULTIPLE";
        if (plantilla.getTipoFirma() != null && !plantilla.getTipoFirma().trim().isEmpty()) {
            tipoFirma = plantilla.getTipoFirma();
        } else if (request.getTipoFirma() != null && !request.getTipoFirma().trim().isEmpty()) {
            tipoFirma = request.getTipoFirma();
        }
        archivoFirma.setTipoFirma(tipoFirma);

        StringBuilder emailFirmantes = new StringBuilder();
        for (int i = 0; i < firmantesParaEstaPlantilla.size(); i++) {
            if (i > 0) emailFirmantes.append(",");
            emailFirmantes.append(firmantesParaEstaPlantilla.get(i).getEmail());
        }
        archivoFirma.setEmailFirmantes(emailFirmantes.toString());

        archivoFirmaService.update(archivoFirma);

        logger.info("✅ ArchivoFirma desde plantilla creado (sin tokens) - ID: " + archivoFirma.getIdArchivoFirma());

        return archivoFirma;

    } catch (Exception e) {
        logger.severe("Error creando ArchivoFirma desde plantilla (sin tokens): " + e.getMessage());
        throw new FirmaException("Error creando archivo de firma desde plantilla: " + e.getMessage());
    }
}

/**
 * Procesa múltiples plantillas SIN generar tokens - CORREGIDO PARA INCLUIR TODOS LOS IDS
 */
private Map<String, Object> procesarMultiplesPlantillasSinTokens(SolicitudFirmaUnificadaDTO request, String ipAddress) throws FirmaException {
    Map<String, Object> resultado = new HashMap<>();
    List<Map<String, Object>> todosDocumentos = new ArrayList<>();
    List<Long> idsArchivosCreados = new ArrayList<>(); // **INCLUIR TODOS LOS IDS PARA TOKENS**
    List<String> nombresArchivos = new ArrayList<>();
    List<String> rutasCompletas = new ArrayList<>();
    int totalSolicitudesCreadas = 0;
    
    try {
        logger.info("=== PROCESANDO MÚLTIPLES PLANTILLAS PARA TOKENS AGRUPADOS ===");
        logger.info("Total plantillas a procesar: " + (request.getPlantillas() != null ? request.getPlantillas().size() : 0));
        
        for (PlantillaRequestDTO plantillaRequest : request.getPlantillas()) {
            Long idPlantilla = plantillaRequest.getIdPlantilla();
            logger.info("🔄 Procesando plantilla ID: " + idPlantilla);
            
            // **CREAR REQUEST TEMPORAL PARA CADA PLANTILLA**
            SolicitudFirmaUnificadaDTO requestTemporal = new SolicitudFirmaUnificadaDTO();
            requestTemporal.setIdUsuario(request.getIdUsuario());
            requestTemporal.setFirmantes(request.getFirmantes());
            requestTemporal.setTipoOrden(request.getTipoOrden());
            requestTemporal.setFechaVigencia(request.getFechaVigencia());
            requestTemporal.setTipoFirma(request.getTipoFirma());
            requestTemporal.setDescripcionPersonalizada(plantillaRequest.getDescripcion());
            requestTemporal.setIdPlantilla(idPlantilla);
            
            // PROCESAR CADA PLANTILLA SIN TOKENS
            Map<String, Object> resultadoPlantilla = procesarPlantillaSinTokens(requestTemporal, ipAddress);
            
            // **🎯 AGREGAR TODOS LOS IDS CREADOS (INCLUYENDO TyC INDIVIDUALES)**
            List<Long> idsDeEstaPlantilla = (List<Long>) resultadoPlantilla.get("idsArchivosCreados");
            if (idsDeEstaPlantilla != null && !idsDeEstaPlantilla.isEmpty()) {
                for (Long idArchivo : idsDeEstaPlantilla) {
                    idsArchivosCreados.add(idArchivo);
                    logger.info("✅ ID agregado para tokens agrupados: " + idArchivo);
                }
            }
            
            // **AGREGAR TODOS LOS NOMBRES Y RUTAS (INCLUYENDO TyC)**
            List<String> nombresDeEstaPlantilla = (List<String>) resultadoPlantilla.get("nombresArchivos");
            List<String> rutasDeEstaPlantilla = (List<String>) resultadoPlantilla.get("rutasCompletas");
            
            if (nombresDeEstaPlantilla != null) {
                nombresArchivos.addAll(nombresDeEstaPlantilla);
                logger.info("📄 Nombres agregados: " + nombresDeEstaPlantilla);
            }
            if (rutasDeEstaPlantilla != null) {
                rutasCompletas.addAll(rutasDeEstaPlantilla);
                logger.info("📁 Rutas agregadas: " + rutasDeEstaPlantilla.size() + " rutas");
            }
            
            // SUMAR SOLICITUDES CREADAS
            Integer solicitudesEstaPlantilla = (Integer) resultadoPlantilla.get("totalSolicitudesCreadas");
            if (solicitudesEstaPlantilla != null) {
                totalSolicitudesCreadas += solicitudesEstaPlantilla;
            }
            
            // AGREGAR DOCUMENTOS PROCESADOS
            List<Map<String, Object>> documentosEstaPlantilla = (List<Map<String, Object>>) resultadoPlantilla.get("solicitudesIndividuales");
            if (documentosEstaPlantilla != null) {
                todosDocumentos.addAll(documentosEstaPlantilla);
            }
        }
        
        logger.info("✅ RESUMEN FINAL PARA TOKENS AGRUPADOS:");
        logger.info("   - Total plantillas procesadas: " + request.getPlantillas().size());
        logger.info("   - Total archivos para tokens: " + idsArchivosCreados.size());
        logger.info("   - IDs incluidos: " + idsArchivosCreados);
        logger.info("   - Nombres incluidos: " + nombresArchivos);
        
        // CONSTRUIR RESPUESTA CON TODOS LOS IDS PARA TOKENS
        resultado.put("todosDocumentos", todosDocumentos);
        resultado.put("idsArchivosCreados", idsArchivosCreados); // **INCLUYE TyC + NORMALES**
        resultado.put("nombresArchivos", nombresArchivos);       // **INCLUYE TyC + NORMALES**
        resultado.put("rutasCompletas", rutasCompletas);         // **INCLUYE TyC + NORMALES**
        resultado.put("totalSolicitudesCreadas", totalSolicitudesCreadas);
        
        return resultado;
        
    } catch (Exception e) {
        logger.severe("Error procesando múltiples plantillas para tokens agrupados: " + e.getMessage());
        throw new FirmaException("Error procesando plantillas: " + e.getMessage());
    }
}

/**
 * Método de depuración para verificar estado de órdenes
 */
public void verificarEstadoOrdenes(Long idArchivoFirma) throws FirmaException {
    try {
        logger.info("=== DEPURACIÓN: ESTADO DE ÓRDENES ===");
        logger.info("Archivo ID: " + idArchivoFirma);
        
        List<SolicitudFirma> todasSolicitudes = solicitudFirmaService.findAllByIdArchivoFirma(idArchivoFirma);
        
        // Agrupar por orden
        Map<Integer, List<SolicitudFirma>> solicitudesPorOrden = new HashMap<>();
        for (SolicitudFirma solicitud : todasSolicitudes) {
            int orden = solicitud.getOrdenFirma();
            solicitudesPorOrden.computeIfAbsent(orden, k -> new ArrayList<>()).add(solicitud);
        }
        
        // Mostrar estado por orden
        for (Map.Entry<Integer, List<SolicitudFirma>> entry : solicitudesPorOrden.entrySet()) {
            int orden = entry.getKey();
            List<SolicitudFirma> solicitudesOrden = entry.getValue();
            
            int firmadas = 0;
            int pendientes = 0;
            
            logger.info("📋 ORDEN " + orden + ":");
            for (SolicitudFirma solicitud : solicitudesOrden) {
                if (solicitud.isFirmado()) {
                    firmadas++;
                } else {
                    pendientes++;
                }
                logger.info("   - " + solicitud.getEmailFirmante() + 
                           " (firmado: " + solicitud.isFirmado() + 
                           ", fecha firma: " + solicitud.getFechaFirma() + ")");
            }
            
            logger.info("📊 Resumen orden " + orden + ": " + firmadas + " firmadas, " + pendientes + " pendientes");
        }
        
    } catch (Exception e) {
        logger.severe("Error en depuración de órdenes: " + e.getMessage());
    }
}

/**
 * Método de debug para monitorear el flujo secuencial completo
 */
public void debugFlujoSecuencial(Long idArchivoFirma) {
    try {
        logger.info("=== 🔍 DEBUG FLUJO SECUENCIAL COMPLETO ===");
        logger.info("📁 Archivo ID: " + idArchivoFirma);
        
        // Obtener archivo
        ArchivoFirma archivo = archivoFirmaService.findById(idArchivoFirma);
        if (archivo == null) {
            logger.warning("⚠️ Archivo no encontrado");
            return;
        }
        
        logger.info("📄 Archivo: " + archivo.getNombreArchivo());
        logger.info("🎯 Tipo firma: " + archivo.getTipoFirma());
        
        // Obtener todas las solicitudes
        List<SolicitudFirma> solicitudes = solicitudFirmaService.findAllByIdArchivoFirma(idArchivoFirma);
        logger.info("📋 Total solicitudes: " + solicitudes.size());
        
        if (solicitudes.isEmpty()) {
            logger.warning("⚠️ No hay solicitudes de firma");
            return;
        }
        
        // Verificar tipo de orden
        String tipoOrden = solicitudes.get(0).getTipoOrdenFirma();
        logger.info("🔄 Tipo orden: " + tipoOrden);
        
        // Agrupar por orden
        Map<Integer, List<SolicitudFirma>> porOrden = new HashMap<>();
        for (SolicitudFirma sol : solicitudes) {
            porOrden.computeIfAbsent(sol.getOrdenFirma(), k -> new ArrayList<>()).add(sol);
        }
        
        // Mostrar estado por orden
        List<Integer> ordenes = new ArrayList<>(porOrden.keySet());
        Collections.sort(ordenes);
        
        for (Integer orden : ordenes) {
            List<SolicitudFirma> solsOrden = porOrden.get(orden);
            long firmadas = solsOrden.stream().mapToLong(s -> s.isFirmado() ? 1 : 0).sum();
            long pendientes = solsOrden.size() - firmadas;
            
            String estado = (pendientes == 0) ? "✅ COMPLETADO" : 
                           (firmadas > 0) ? "⏳ EN PROCESO" : "⏸️ ESPERANDO";
            
            logger.info("📊 ORDEN " + orden + ": " + estado + " (" + firmadas + "/" + solsOrden.size() + " firmadas)");
            
            for (SolicitudFirma sol : solsOrden) {
                String estadoFirma = sol.isFirmado() ? "✅" : "⏳";
                logger.info("   " + estadoFirma + " " + sol.getEmailFirmante() + 
                           (sol.getFechaFirma() != null ? " (firmado: " + sol.getFechaFirma() + ")" : ""));
            }
        }
        
        // Determinar orden activo
        Integer ordenActivo = null;
        for (Integer orden : ordenes) {
            List<SolicitudFirma> solsOrden = porOrden.get(orden);
            boolean tieneTodasFirmadas = solsOrden.stream().allMatch(SolicitudFirma::isFirmado);
            
            if (!tieneTodasFirmadas) {
                ordenActivo = orden;
                break;
            }
        }
        
        if (ordenActivo != null) {
            logger.info("🎯 ORDEN ACTIVO: " + ordenActivo);
        } else {
            logger.info("🏁 PROCESO COMPLETADO: Todos los órdenes firmados");
        }
        
        logger.info("=== 🔍 FIN DEBUG FLUJO SECUENCIAL ===");
        
    } catch (Exception e) {
        logger.severe("Error en debug flujo secuencial: " + e.getMessage());
    }
}

}