/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.service;

import java.util.logging.Level;
import java.util.logging.Logger;

import se.firme.commons.firmese.dto.DatosEmailMultipleFirmaDTO;
import se.firme.commons.firmese.enumerados.EParametro;
import se.firme.commons.firmese.util.Parameters;

/**
 * @document EmailTemplateService
 * <AUTHOR>
 * @fecha viernes, agosto 21 de 2020, 04:08:17 PM
 */
public class EmailTemplateService {
	/**
	 * Envío de correo para completar registro de usuario
	 * 
	 * @param token       Token de validación de correo
	 * @param urlFrontend url del front en el ambiente que está corriendo la
	 *                    aplicación
	 * @return
	 */
	public static String registrationEmailTemplate(String token, String urlFrontend) {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>Estimado usuario</h2>");
		builder.append("<p>¡Felicitaciones, hemos recibido tu solicitud de registro en <b>Fírme.se</b>!.</p>");
		builder.append("<p>Para verificar el correo electrónico por favor haz clic en el enlace a continuación: ");

		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/validar?tkn=");
		builder.append(token);
		builder.append("' target='_blank'>clic aquí</a></p>");
		builder.append(
				"<p>Si tienes problemas para abrir el enlace, por favor copia y pega la url a continuación en el navegador de tu preferencia</p>");
		builder.append("<code>");
		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/validar?tkn=");
		builder.append(token);
		builder.append("'>");
		builder.append(urlFrontend);
		builder.append("/validar?tkn=");
		builder.append(token);
		builder.append("</a>");
		builder.append("</code>");
		builder.append("<br/>");
		builder.append(
				"<p>Recuerda seguir las instrucciones del enlace para que puedas completar el registro de usuario.</p>");
		builder.append("<br/>");
		builder.append("<p>Saludos cordiales.</p>");
		builder.append("</div>");

		return builder.toString();
	}

	public static String notificacionFirmasEmailTemplate() {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>Estimado usuario</h2>");
		builder.append("<p>Esta es una notificación de firma de documentos en la plataforma <b>Fírmese</b>.</p>");
		builder.append("<p>A continuación se anexan los documentos firmados exitosamente.</p>");

		return builder.toString();
	}
	
	

	public static String contenidoEnvioOTP(String codigo) {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>Estimado usuario</h2>");
		builder.append("<p>A continuación indicamos tu código de verificación para firma de documentos en la plataforma <b>Fírmese</b>.</p>");
		builder.append("<h4>");
		builder.append(codigo);
		builder.append("</h4>");
		builder.append("<p>Saludos cordiales.</p>");

		return builder.toString();
	}
	
	public static String cambioContrasenaTemplate(String token, String urlFrontend) {
		StringBuilder builder = new StringBuilder();
		builder.append("<div>");
		builder.append("<h2>Estimado usuario</h2>");
		builder.append("<p>Hemos recibido una solicitud para cambio de contraseña <b>Fírme.se</b>!.</p>");
		builder.append("<p>Para continuar con el proceso por favor haz clic en el enlace a continuación: ");

		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/change-passwd/");
		builder.append(token);
		builder.append("' target='_blank'>clic aquí</a></p>");
		builder.append(
				"<p>Si tienes problemas para abrir el enlace, por favor copia y pega la url a continuación en el navegador de tu preferencia</p>");
		builder.append("<code>");
		builder.append("<a href='");
		builder.append(urlFrontend);
		builder.append("/change-passwd/");
		builder.append(token);
		builder.append("'>");
		builder.append(urlFrontend);
		builder.append("/change-passwd/");
		builder.append(token);
		builder.append("</a>");
		builder.append("</code>");
		builder.append("<br/>");

		builder.append("<p>Saludos cordiales.</p>");
		builder.append("</div>");

		return builder.toString();
	}

	public static String getTemplateFirmaMultiple(String token, String urlFrontend, DatosEmailMultipleFirmaDTO datosCorreo) {
	    // **AGREGAR LOGGING PARA DEBUG**
	    System.out.println("🔍 === GENERANDO TEMPLATE FIRMA MULTIPLE ===");
	    System.out.println("   🎫 Token: " + (token != null ? token.substring(0, Math.min(10, token.length())) + "..." : "NULL"));
	    System.out.println("   🌐 URL Frontend: " + urlFrontend);

	    if (datosCorreo == null) {
	        System.out.println("❌ ERROR CRÍTICO: datosCorreo es NULL - esto generará error en la plantilla");
	        // Crear datos de emergencia para evitar NullPointerException
	        datosCorreo = new DatosEmailMultipleFirmaDTO();
	        datosCorreo.setNombreFirmante("Usuario");
	        datosCorreo.setNombreRemitente("Sistema");
	        datosCorreo.setEmailRemitente("<EMAIL>");
	        datosCorreo.setNombreDocumento(java.util.Arrays.asList("Documento"));
	    } else {
	        System.out.println("   👤 Datos del correo:");
	        System.out.println("      - Remitente: " + datosCorreo.getNombreRemitente() + " (" + datosCorreo.getEmailRemitente() + ")");
	        System.out.println("      - Firmante: " + datosCorreo.getNombreFirmante());
	        System.out.println("      - Documentos: " + datosCorreo.getNombreDocumento());
	    }

	    String enlace = urlFrontend + "/multiple-firma/" + token;
	    StringBuilder builder = new StringBuilder();

	    builder.append("<div style='font-family: Arial, sans-serif; line-height: 1.5;'>");

	    // Saludo
	    builder.append("<p>Hola, ")
	           .append(datosCorreo.getNombreFirmante())
	           .append(",</p>");

	    // Remitente
	    builder.append("<p>")
	           .append(datosCorreo.getNombreRemitente())
	           .append(" (")
	           .append(datosCorreo.getEmailRemitente())
	           .append(") le ha enviado una <b>solicitud de firma electrónica</b> a través de la plataforma <b>Fírmese</b>.</p>");

	    // Lista de documentos
	    builder.append("<p><b>Documentos incluidos en esta solicitud:</b></p><ul>");
	    for (String nombreDoc : datosCorreo.getNombreDocumento()) {
	        builder.append("<li>").append(nombreDoc).append("</li>");
	    }
	    builder.append("</ul>");

	    // Instrucción principal
	    builder.append("<p>Para iniciar el proceso, por favor, haga clic en el siguiente botón. "
	                 + "Será dirigido a la plataforma de <b>Fírmese</b>, un entorno seguro donde podrá "
	                 + "revisar cada documento en su totalidad antes de aplicar su firma.</p>");

	    // Botón principal
	    builder.append("<p><a href='")
	           .append(enlace)
	           .append("' target='_blank' style='display:inline-block;padding:10px 20px;"
	                 + "background-color:#2EE0A7;color:white;text-decoration:none;border-radius:5px;'>")
	           .append("Iniciar Proceso de Firma</a></p>");

	    // Enlace alternativo
	    builder.append("<p>Si el botón anterior no funciona, puede copiar y pegar el siguiente enlace en su navegador:</p>");
	    builder.append("<p><a href='")
	           .append(enlace)
	           .append("' target='_blank'>")
	           .append(enlace)
	           .append("</a></p>");

	    // Despedida
	    builder.append("<p>Gracias por utilizar nuestra plataforma.</p>");

	    builder.append("</div>");

	    return builder.toString();
	}
	
	// Método para generar asunto personalizado 
	public static String getAsuntoFirmaPersonalizado(DatosEmailMultipleFirmaDTO datosCorreo) {
		return "Solicitud de firma de " + 
			(datosCorreo.getNombreRemitente() != null ? datosCorreo.getNombreRemitente() : "un usuario") + 
			" para los documentos adjuntos";
	}

}

