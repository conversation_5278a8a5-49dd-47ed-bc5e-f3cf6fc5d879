/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package se.firme.ms.token.negocio;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import co.venko.ms.models.entity.AdmUsuario;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.RegistroDTO;
import se.firme.commons.firmese.dto.SolicitudFirmaDTO;
import se.firme.commons.firmese.dto.SolicitudFirmaDTOv2;
import se.firme.commons.firmese.service.EmailService;
import se.firme.commons.firmese.service.EmailTemplateService;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;
import se.firme.ms.datos.models.entity.ProcesoFirma;
import se.firme.ms.datos.models.entity.Token;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.ArchivoFirmaServiceImpl;
import se.firme.ms.models.service.FirmaArchivoUsuarioServiceImpl;
import se.firme.ms.models.service.ProcesoFirmaServiceImpl;
import se.firme.ms.models.service.TokenServiceImpl;
import se.firme.ms.models.service.UsuarioServiceImpl;
import se.firme.ms.models.service.helper.ArchivoFirmaHelper;
import se.firme.ms.models.service.helper.UsuarioHelper;
import se.firme.ms.token.rest.client.UsuarioAdminClient;
import se.firme.ms.token.service.MensajeroService;

/**
 * @document TokenServicio
 * <AUTHOR> Machado Jaimes
 * @fecha martes, agosto 18 de 2020, 07:14:11 PM
 */
@Service
public class TokenNegocio {

    @Autowired
    private TokenServiceImpl tokenService;
    @Autowired
    private UsuarioServiceImpl usuarioServiceImpl;
    @Autowired
    private Environment env;
    @Autowired
    private ArchivoFirmaServiceImpl archivoFirmaServiceImpl;
    @Autowired
    private ProcesoFirmaServiceImpl firmaServiceImpl;
    @Autowired
    private UsuarioAdminClient usuarioVMSClient;
    @Autowired
    private MensajeroService mensajeroService;
    @Autowired
    private FirmaArchivoUsuarioServiceImpl archivoUsuarioServiceImpl;

    private static Logger logger = Logger.getLogger(TokenNegocio.class.getName());

    public RegistroDTO consultarToken(String codigoToken) throws FirmaException {
        try {
            Token token = tokenService.consultarToken(codigoToken);
            if (token != null) {
                RegistroDTO registroDTO = new RegistroDTO();
                registroDTO.setIdUsuario(token.getIdUsuario().getIdUsuario());
                registroDTO.setCodigoTransaccion(token.getCodigoTransaccion());
                Usuario usuario = token.getIdUsuario();
                usuario.setActivo(true);
                AdmUsuario admUsuario = UsuarioHelper.convert(usuario);
                usuarioVMSClient.guardarUsuarioAdm(admUsuario);
                return registroDTO;
            }
            throw new FirmaException("No existe token");
        } catch (Exception e) {
            logger.severe("Token: " + codigoToken + " :: Error: " + e.getMessage());
            throw new FirmaException("" + e.getMessage());
        }
    }

    public Object crearTokenCambioContrasena(String email) throws FirmaException {
    	// manejo de errores en obtención del json
    	try {
    		logger.info("Cambio de contraseña: Email para: "+ email);
    		JsonElement element = new JsonParser().parse(email);
            JsonObject jsonObject = element.getAsJsonObject();
            email = jsonObject.get("username").getAsString();
    	} catch(Exception e) {
    		throw new FirmaException("Cambio de contraseña: Error en la obtención del JSON: " + e.getMessage());
    	}
    	// findByEmail controla la excepción en caso de no encontrar registro o fallo en la base de datos
    	Usuario usuario = usuarioServiceImpl.findByEmail(email);
    	
    	// si no encuentra el usuario devuelve null, maneja el error en la base de datos
    	Token token = tokenService.consultarTokenUsuario(email, Parameters.string.TOKEN_TIPO_CAMBIO_CONTRASENA);
    	
    	if(token == null) {
    		logger.info("Cambio de contraseña: No existe token");
    		// al token se le pone 24 horas de duración, los parámetros vienen hardcodeados y admite nulls, no levanta errores
            try{
                token = tokenService.getNuevoToken(usuario, Parameters.string.TOKEN_TIPO_CAMBIO_CONTRASENA, 30, null,
                    null, null);
            }catch(Exception e){
            	throw new FirmaException("Cambio de contraseña: No se pudo crear el token");
            }

    	}
    	// validar que tiene una fecha de vencimiento
    	if(token.getFechaVencimiento() == null) {
    		throw new FirmaException("Cambio de contraseña: La fecha de vencimiento del token no puede ser null");
    	}
    	// Si existe el token validar la fecha de vencimiento
    	Date ahora = new Date();
    	if(token.getFechaVencimiento().before(ahora)) {
    		token=tokenService.actualizarFechaVencimiento(token);
    	}
    	try {
    		logger.info("Cambio de contraseña: Enviando el token al correo: ");
			EmailService.send(usuario.getCorreoElectronico(), "Solicitud de cambio de contraseña",
                    EmailTemplateService.cambioContrasenaTemplate(token.getIdToken(),
                            env.getProperty(Parameters.string.FIRMESE_BASE_URI_FRONTEND)));
		} catch(Exception e) {
			throw new FirmaException("Cambio de contraseña: No se pudo enviar el correo");
		}
    	return "Se ha reenviado la solicitud de cambio de contraseña al correo " + Utilities.maskMobileNumber(email) + " ";
    	
    }
    
    public SolicitudFirmaDTO getArchivoFirmaByToken(String tkn) throws FirmaException {
        try {
            Token token = tokenService.consultarToken(tkn);
            if (token != null) {
                logger.info("Email de firmante: " + token.getEmailFirmante());
                if (token.getActivo()) {
                    List<ArchivoFirma> archivos = archivoFirmaServiceImpl.buscarArchivosFirma(token.getIds());
                    List<ArchivoFirmaResponseDTO> listaDTO = new ArrayList<>();
                    for (ArchivoFirma archivo : archivos) {
                        ArchivoFirmaResponseDTO dto = ArchivoFirmaHelper.getArchivoFirmaHandle(archivo);
                        dto.setEmailFirmantes(null);
                        listaDTO.add(dto);
                    }

                    List<FirmaArchivoUsuario> firmas = archivoUsuarioServiceImpl.findByUsuario(token.getIdUsuario().getIdUsuario());
                    boolean nuevo = true;
                    if (firmas != null && !firmas.isEmpty()) {
                        nuevo = false;
                    }
                    Usuario usuario = usuarioServiceImpl.findById(archivos.get(0).getIdUsuario());
                    return new SolicitudFirmaDTO(nuevo).archivos(listaDTO).solicitante(usuario.getNombreCompleto())
                            .email(usuario.getCorreoElectronico()).build();
                }
                throw new FirmaException("Token inactivo");
            }
            throw new FirmaException("No se encontró ningun registro de token");
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }
    
    public SolicitudFirmaDTOv2 getArchivoFirmaByTokenV2(String tkn) throws FirmaException {
        logger.info("utilizando la v2 del endpoint verifica-token-firmante");
        Token token = tokenService.consultarToken(tkn);
        List<ArchivoFirma> archivos = archivoFirmaServiceImpl.buscarArchivosFirma(token.getIds());
        
        // Declarar variables fuera de los try-catch
        List<ArchivoFirmaResponseDTO> listaDTO = null;
        Usuario usuarioRemitente = null;
        try{
            logger.info("Email de firmante: " + token.getEmailFirmante());
            listaDTO = new ArrayList<>();
            for (ArchivoFirma archivo : archivos) {
                ArchivoFirmaResponseDTO dto = ArchivoFirmaHelper.getArchivoFirmaHandle(archivo);
                dto.setEmailFirmantes(null);
                listaDTO.add(dto);
            }
        } catch(Exception e){
            throw new FirmaException("Error al consultar los archivos asociados al token: " + e.getMessage());
        }
        // obtención del usuario remitente
        try {
            usuarioRemitente = usuarioServiceImpl.findById(archivos.get(0).getIdUsuario());
        } catch (Exception e) {
            throw new FirmaException("Error al consultar el usuario remitente: " + e.getMessage());
        }
        if (usuarioRemitente == null) {
            throw new FirmaException("No se encontró al usuario remitente.");
        }
        
        //  Creación del DTO base
        SolicitudFirmaDTOv2 solicitud = new SolicitudFirmaDTOv2(true) 
            .archivos(listaDTO)
            .solicitante(usuarioRemitente.getNombreCompleto())
            .email(usuarioRemitente.getCorreoElectronico());

        Usuario usuarioFirmante = usuarioServiceImpl.findByEmail(token.getEmailFirmante());
        logger.info("Firmante encontrado en BD: " + usuarioFirmante.getNombreCompleto());
        // Llenar los datos básicos del firmante
        solicitud.setNombreFirmante(usuarioFirmante.getNombreCompleto());
        solicitud.setNumeroDocumentoFirmante(usuarioFirmante.getNumeroDocumento());
        solicitud.setCorreoElectronicoFirmante(usuarioFirmante.getCorreoElectronico());
        solicitud.setNumeroCelularFirmante(usuarioFirmante.getNumeroCelular());

        //  Lógica condicional a prueba de nulos 
        Long idReferido=null;
        logger.info("el id referido es");
        try {
            idReferido = usuarioFirmante.getIdReferido(); // Obtenemos el ID
        }
        catch(NullPointerException e) {
            logger.info("el usuario no tiene referido");
        }   
        logger.info("el usuario referido es: "+idReferido);
        // verficación
        boolean fueRegistradoPorOris = idReferido != null && idReferido > 0;
        logger.info("fue registrado por oris es: "+fueRegistradoPorOris);
        boolean noHaFirmadoTyC = !usuarioFirmante.getFirmadoTyc();
        int totalFirmasReales = archivoUsuarioServiceImpl.contarFirmasUsuario(usuarioFirmante.getIdUsuario());
        boolean nuncaHaFirmado = totalFirmasReales == 0;
                
        boolean debeConfirmarDatos = fueRegistradoPorOris && noHaFirmadoTyC && nuncaHaFirmado;

        solicitud.setFirmanteRegistrado(debeConfirmarDatos); 
        logger.info("¿Debe confirmar datos (mostrar tabla)?: " + debeConfirmarDatos);
        return solicitud.build();
    }

    public String aceptarTokenFirmante(String tkn) throws FirmaException {
        try {
            Token token = tokenService.consultarToken(tkn);
            if (token != null) {
                if (token.getActivo()) {
                    Usuario usuario = usuarioServiceImpl.findByEmail(token.getEmailFirmante());
                    if (usuario != null) {
                        if (usuario.getActivo()) {
                            ProcesoFirma proceso = firmaServiceImpl.getNuevoProcesoFirma(usuario, token.getIdToken());
                            logger.info("Código sms creado:" + proceso.getCodigoSms());
                            StringBuilder builder = new StringBuilder();
//		                builder.append("<#> ");
                            builder.append("Firme.se\n\n");
                            builder.append("Tu codigo para completar el proceso de firma es: ");
                            builder.append(proceso.getCodigoSms());
//		                builder.append("\nvgmCCOrf5mD");/*VtXeXco0bNT*/
                            // Utils.sendMessage(builder.toString(),
                            // usuario.getNumeroCelular(),Utils.CLIENTE_VONAGE);

                            mensajeroService.notificar(builder.toString(), usuario.getNumeroCelular());
                            return usuario.getIdUsuario() + "";
                        }
                        throw new FirmaException(
                                "Usuario aún no está activo, por favor completar el proceso de registro de usuario en Fírme.se");
                    }
                    throw new FirmaException(
                            "No existe registro de usuario en la aplicación Fírme.se, por favor registrate para que puedas continuar con la firma requerida");
                }
                throw new FirmaException("Token inactivo, ya no puedes firmar este documento");
            }
            throw new FirmaException("No se encontró ningun registro de token");
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    public void solicitarNuevoToken(String codigo) throws FirmaException {
        try {
            Token token = tokenService.findTokenByID(codigo);
            if (token.getActivo()) {
                logger.info("Token-1  tipo: " + token.getTipo());
                token = tokenService.getTokenActivoUsuario(token.getIdUsuario());
                logger.info("Token 1: " + codigo);
                logger.info("Token 2: " + token.getIdToken());
                logger.info("Token-2 tipo: " + token.getTipo());
                if (EmailService.send(token.getIdUsuario().getCorreoElectronico(), "Activación de cuenta",
                        EmailTemplateService.registrationEmailTemplate(token.getIdToken(),
                                env.getProperty("app.web.frontend")))) {
                    tokenService.desactivarToken(codigo);
                }
            }
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }

    }
}
